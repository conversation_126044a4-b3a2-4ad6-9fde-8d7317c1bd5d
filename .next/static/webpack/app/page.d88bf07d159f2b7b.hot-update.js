"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/DataTable.tsx":
/*!**************************************!*\
  !*** ./src/components/DataTable.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataTable: function() { return /* binding */ DataTable; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _store_formStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/formStore */ \"(app-pages-browser)/./src/store/formStore.ts\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_FaBitcoin_FaDice_FaDollarSign_FaHeart_FaLeaf_FaSmoking_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=FaBitcoin,FaDice,FaDollarSign,FaHeart,FaLeaf,FaSmoking!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DataTable auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Grey niche icons mapping\nconst greyNicheIcons = {\n    casino: _barrel_optimize_names_FaBitcoin_FaDice_FaDollarSign_FaHeart_FaLeaf_FaSmoking_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaDice,\n    cbd: _barrel_optimize_names_FaBitcoin_FaDice_FaDollarSign_FaHeart_FaLeaf_FaSmoking_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaLeaf,\n    crypto: _barrel_optimize_names_FaBitcoin_FaDice_FaDollarSign_FaHeart_FaLeaf_FaSmoking_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaBitcoin,\n    forex: _barrel_optimize_names_FaBitcoin_FaDice_FaDollarSign_FaHeart_FaLeaf_FaSmoking_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaDollarSign,\n    adult: _barrel_optimize_names_FaBitcoin_FaDice_FaDollarSign_FaHeart_FaLeaf_FaSmoking_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaHeart,\n    vaping: _barrel_optimize_names_FaBitcoin_FaDice_FaDollarSign_FaHeart_FaLeaf_FaSmoking_react_icons_fa__WEBPACK_IMPORTED_MODULE_6__.FaSmoking\n};\n// Helper functions to get country and language display info\nconst getCountryDisplay = (countryCode)=>{\n    const country = _lib_utils__WEBPACK_IMPORTED_MODULE_5__.countryOptions.find((opt)=>opt.value === countryCode);\n    return country ? {\n        name: country.label,\n        flag: country.flag\n    } : {\n        name: countryCode,\n        flag: \"\\uD83C\\uDFF3️\"\n    };\n};\nconst getLanguageDisplay = (languageCode)=>{\n    const language = _lib_utils__WEBPACK_IMPORTED_MODULE_5__.languageOptions.find((opt)=>opt.value === languageCode);\n    return language ? {\n        name: language.label,\n        flag: language.flag\n    } : {\n        name: languageCode,\n        flag: \"\\uD83C\\uDFF3️\"\n    };\n};\nfunction DataTableComponent() {\n    _s();\n    const { websites, pagination, isLoading, setCurrentPage } = (0,_store_formStore__WEBPACK_IMPORTED_MODULE_4__.useFormStore)();\n    // Show all websites without filtering\n    const filteredWebsites = websites;\n    const paginatedWebsites = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const startIndex = (pagination.currentPage - 1) * pagination.pageSize;\n        const endIndex = startIndex + pagination.pageSize;\n        return filteredWebsites.slice(startIndex, endIndex);\n    }, [\n        filteredWebsites,\n        pagination.currentPage,\n        pagination.pageSize\n    ]);\n    const totalPages = Math.ceil(filteredWebsites.length / pagination.pageSize);\n    // Get all grey niche icons (all 6 compulsory)\n    const getAllGreyNicheIcons = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        return Object.keys(greyNicheIcons);\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border rounded-lg overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.Table, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                            className: \"[&_tr]:border-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableRow, {\n                                className: \"bg-[#faf8ff] border-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                        className: \"py-4 text-gray-600 text-base\",\n                                        children: \"Website\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                        className: \"py-4 text-gray-600 text-base\",\n                                        children: \"Country\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                        className: \"py-4 text-gray-600 text-base\",\n                                        children: \"Language\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                        className: \"py-4 text-gray-600 text-base\",\n                                        children: \"Category\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                        className: \"py-4 text-gray-600 text-base\",\n                                        children: \"Grey Niche\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableBody, {\n                            className: \"[&_tr:last-child]:border-0 [&_tr]:border-0\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableRow, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    colSpan: 5,\n                                    className: \"text-center py-8\",\n                                    children: \"Loading...\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                lineNumber: 97,\n                                columnNumber: 15\n                            }, this) : paginatedWebsites.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableRow, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    colSpan: 5,\n                                    className: \"text-center py-8\",\n                                    children: \"No websites found\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, this) : paginatedWebsites.map((website, index)=>{\n                                const countryDisplay = getCountryDisplay(website.trafficCountry);\n                                const languageDisplay = getLanguageDisplay(website.primaryLanguage);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableRow, {\n                                    className: \"border-0 hover:bg-gray-50 \".concat(index % 2 === 0 ? \"bg-white\" : \"bg-[#faf8ff]\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                            className: \"py-4 text-black text-base font-medium\",\n                                            children: website.websiteUrl\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                            className: \"py-4 text-black text-base\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-lg\",\n                                                        children: countryDisplay.flag\n                                                    }, void 0, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    countryDisplay.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                            className: \"py-4 text-black text-base\",\n                                            children: languageDisplay.name\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                            lineNumber: 133,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                            className: \"py-4 text-black text-base\",\n                                            children: website.categories.join(\", \")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                            className: \"py-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: getAllGreyNicheIcons().map((niche)=>{\n                                                    const IconComponent = greyNicheIcons[niche];\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-base text-gray-600\",\n                                                        title: niche,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {}, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                                            lineNumber: 149,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    }, niche, false, {\n                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 29\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, website.id, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 19\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, this),\n            totalPages > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-700\",\n                        children: [\n                            \"Showing \",\n                            (pagination.currentPage - 1) * pagination.pageSize + 1,\n                            \" to\",\n                            \" \",\n                            Math.min(pagination.currentPage * pagination.pageSize, filteredWebsites.length),\n                            \" \",\n                            \"of \",\n                            filteredWebsites.length,\n                            \" results\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>setCurrentPage(pagination.currentPage - 1),\n                                disabled: pagination.currentPage === 1,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Previous\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-1\",\n                                children: Array.from({\n                                    length: totalPages\n                                }, (_, i)=>i + 1).map((page)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: page === pagination.currentPage ? \"default\" : \"outline\",\n                                        size: \"sm\",\n                                        onClick: ()=>setCurrentPage(page),\n                                        className: \"w-8 h-8\",\n                                        children: page\n                                    }, page, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"sm\",\n                                onClick: ()=>setCurrentPage(pagination.currentPage + 1),\n                                disabled: pagination.currentPage === totalPages,\n                                children: [\n                                    \"Next\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n                lineNumber: 165,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/DataTable.tsx\",\n        lineNumber: 72,\n        columnNumber: 5\n    }, this);\n}\n_s(DataTableComponent, \"WQpC1qjpQOzZq9pNwNnvT0NqzQA=\", false, function() {\n    return [\n        _store_formStore__WEBPACK_IMPORTED_MODULE_4__.useFormStore\n    ];\n});\n_c = DataTableComponent;\nconst DataTable = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(DataTableComponent);\n_c1 = DataTable;\nvar _c, _c1;\n$RefreshReg$(_c, \"DataTableComponent\");\n$RefreshReg$(_c1, \"DataTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DataTable.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-website/page",{

/***/ "(app-pages-browser)/./src/components/AddWebsiteForm.tsx":
/*!*******************************************!*\
  !*** ./src/components/AddWebsiteForm.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AddWebsiteForm: function() { return /* binding */ AddWebsiteForm; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var formik__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! formik */ \"(app-pages-browser)/./node_modules/formik/dist/formik.esm.js\");\n/* harmony import */ var _lib_validation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/validation */ \"(app-pages-browser)/./src/lib/validation.ts\");\n/* harmony import */ var _store_formStore__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/formStore */ \"(app-pages-browser)/./src/store/formStore.ts\");\n/* harmony import */ var _InfoCard__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./InfoCard */ \"(app-pages-browser)/./src/components/InfoCard.tsx\");\n/* harmony import */ var _PreconditionsAlert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./PreconditionsAlert */ \"(app-pages-browser)/./src/components/PreconditionsAlert.tsx\");\n/* harmony import */ var _WebsiteDetailsSection__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./WebsiteDetailsSection */ \"(app-pages-browser)/./src/components/WebsiteDetailsSection.tsx\");\n/* harmony import */ var _CreateOfferSection__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./CreateOfferSection */ \"(app-pages-browser)/./src/components/CreateOfferSection.tsx\");\n/* harmony import */ var _ArticleSpecificationSection__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./ArticleSpecificationSection */ \"(app-pages-browser)/./src/components/ArticleSpecificationSection.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* __next_internal_client_entry_do_not_use__ AddWebsiteForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Custom function to convert Zod schema to Formik validation\nconst createFormikValidation = (schema)=>{\n    return (values)=>{\n        try {\n            schema.parse(values);\n            console.log(\"Validation passed for values:\", values);\n            return {};\n        } catch (error) {\n            console.log(\"Validation failed:\", error);\n            const formikErrors = {};\n            if (error.errors) {\n                error.errors.forEach((err)=>{\n                    const path = err.path.join(\".\");\n                    formikErrors[path] = err.message;\n                });\n            }\n            console.log(\"Formik errors:\", formikErrors);\n            return formikErrors;\n        }\n    };\n};\nfunction AddWebsiteFormComponent(param) {\n    let { isEditMode = false } = param;\n    _s();\n    var _s1 = $RefreshSig$();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { formData, updateFormData, isSubmitting, setSubmitting, editingWebsiteId, addWebsite, updateWebsite, resetForm } = (0,_store_formStore__WEBPACK_IMPORTED_MODULE_4__.useFormStore)();\n    const initialValues = {\n        websiteUrl: formData.websiteUrl || \"\",\n        primaryLanguage: formData.primaryLanguage || \"english\",\n        trafficCountry: formData.trafficCountry || \"us\",\n        categories: formData.categories || [],\n        description: formData.description || \"\",\n        isOwner: formData.isOwner || false,\n        guestPostingPrice: formData.guestPostingPrice || 54,\n        linkInsertionPrice: formData.linkInsertionPrice || 54,\n        greyNicheOffers: formData.greyNicheOffers || {\n            casino: {\n                guestPostPrice: 0,\n                linkInsertionPrice: 0\n            },\n            cbd: {\n                guestPostPrice: 0,\n                linkInsertionPrice: 0\n            },\n            crypto: {\n                guestPostPrice: 0,\n                linkInsertionPrice: 0\n            },\n            forex: {\n                guestPostPrice: 0,\n                linkInsertionPrice: 0\n            },\n            adult: {\n                guestPostPrice: 0,\n                linkInsertionPrice: 0\n            },\n            vaping: {\n                guestPostPrice: 0,\n                linkInsertionPrice: 0\n            }\n        },\n        homepageOffer: formData.homepageOffer || {\n            price: 0,\n            description: \"\"\n        },\n        isWritingIncluded: formData.isWritingIncluded || \"yes\",\n        wordCountType: formData.wordCountType || \"unlimited\",\n        minWords: formData.minWords || 0,\n        maxWords: formData.maxWords || 0,\n        allowDofollow: formData.allowDofollow || \"yes\",\n        linkType: formData.linkType || \"brand\",\n        taggingPolicy: formData.taggingPolicy || \"no-tag\",\n        linkNumberType: formData.linkNumberType || \"unlimited\",\n        minLinks: formData.minLinks || 0,\n        maxLinks: formData.maxLinks || 0,\n        otherLinksPolicy: formData.otherLinksPolicy || \"no-allow\",\n        contentRules: formData.contentRules || \"\",\n        acceptedContentTypes: formData.acceptedContentTypes || [\n            \"How-to guides\"\n        ],\n        turnaroundTime: formData.turnaroundTime || 7,\n        revisionPolicy: formData.revisionPolicy || \"one-revision\",\n        contentGuidelines: formData.contentGuidelines || \"\",\n        prohibitedTopics: formData.prohibitedTopics || [],\n        requiredDisclosures: formData.requiredDisclosures || false,\n        socialMediaPromotion: formData.socialMediaPromotion || false,\n        metaDataRequirements: formData.metaDataRequirements || \"\"\n    };\n    const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (values)=>{\n        console.log(\"Form submitted with values:\", values);\n        setSubmitting(true);\n        updateFormData(values);\n        try {\n            // Simulate API call\n            await new Promise((resolve)=>setTimeout(resolve, 2000));\n            if (isEditMode && editingWebsiteId) {\n                // Update existing website\n                updateWebsite(editingWebsiteId, values);\n                setNotification({\n                    type: \"success\",\n                    message: \"Website updated successfully!\"\n                });\n            } else {\n                // Add new website - ensure all required fields are set\n                const websiteData = {\n                    ...values,\n                    minWords: values.minWords || 0,\n                    maxWords: values.maxWords || 0,\n                    minLinks: values.minLinks || 0,\n                    maxLinks: values.maxLinks || 0,\n                    contentRules: values.contentRules || \"\",\n                    contentGuidelines: values.contentGuidelines || \"\",\n                    metaDataRequirements: values.metaDataRequirements || \"\",\n                    homepageOffer: {\n                        price: values.homepageOffer.price,\n                        description: values.homepageOffer.description || \"\"\n                    },\n                    status: \"active\"\n                };\n                addWebsite(websiteData);\n                setNotification({\n                    type: \"success\",\n                    message: \"Website added successfully!\"\n                });\n                resetForm();\n            }\n            // Navigate back to the list after a short delay to show the success message\n            setTimeout(()=>{\n                router.push(\"/\");\n            }, 1500);\n        } catch (error) {\n            console.error(\"Submission error:\", error);\n            setNotification({\n                type: \"error\",\n                message: \"Error submitting form. Please try again.\"\n            });\n        } finally{\n            setSubmitting(false);\n        }\n    }, [\n        isEditMode,\n        editingWebsiteId,\n        updateFormData,\n        updateWebsite,\n        addWebsite,\n        resetForm,\n        router,\n        setSubmitting,\n        setNotification\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative w-full bg-background-25 px-4 md:px-[78px] py-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"font-heading-h2 text-[length:var(--heading-h2-font-size)] tracking-[var(--heading-h2-letter-spacing)] leading-[var(--heading-h2-line-height)] text-foregroundbase mb-12\",\n                children: isEditMode ? \"Edit website\" : \"Add a website\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/AddWebsiteForm.tsx\",\n                lineNumber: 172,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col w-full items-center gap-16\",\n                children: [\n                    notification && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Alert, {\n                        className: \"max-w-2xl \".concat(notification.type === \"success\" ? \"border-green-200 bg-green-50\" : \"border-red-200 bg-red-50\"),\n                        children: [\n                            notification.type === \"success\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CheckCircle, {\n                                className: \"h-4 w-4 text-green-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/AddWebsiteForm.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertCircle, {\n                                className: \"h-4 w-4 text-red-600\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/AddWebsiteForm.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertDescription, {\n                                className: notification.type === \"success\" ? \"text-green-800\" : \"text-red-800\",\n                                children: notification.message\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/AddWebsiteForm.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/AddWebsiteForm.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_InfoCard__WEBPACK_IMPORTED_MODULE_5__.InfoCard, {}, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/AddWebsiteForm.tsx\",\n                        lineNumber: 203,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_11__.Formik, {\n                        initialValues: initialValues,\n                        validate: createFormikValidation(_lib_validation__WEBPACK_IMPORTED_MODULE_3__.websiteFormSchema),\n                        onSubmit: handleSubmit,\n                        enableReinitialize: true,\n                        children: _s1((param)=>{\n                            let { values, setFieldValue, errors, touched, isValid } = param;\n                            _s1();\n                            console.log(\"Form validation state:\", {\n                                isValid,\n                                errors\n                            });\n                            // Auto-save form data as user types (with debouncing)\n                            (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n                                const timeoutId = setTimeout(()=>{\n                                    updateFormData(values);\n                                }, 500); // 500ms debounce\n                                return ()=>clearTimeout(timeoutId);\n                            }, [\n                                values\n                            ]);\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(formik__WEBPACK_IMPORTED_MODULE_11__.Form, {\n                                className: \"flex flex-col items-center gap-[73px] w-full\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PreconditionsAlert__WEBPACK_IMPORTED_MODULE_6__.PreconditionsAlert, {}, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/AddWebsiteForm.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_WebsiteDetailsSection__WEBPACK_IMPORTED_MODULE_7__.WebsiteDetailsSection, {\n                                        values: values,\n                                        setFieldValue: setFieldValue,\n                                        errors: errors,\n                                        touched: touched\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/AddWebsiteForm.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CreateOfferSection__WEBPACK_IMPORTED_MODULE_8__.CreateOfferSection, {\n                                        values: values,\n                                        setFieldValue: setFieldValue,\n                                        errors: errors,\n                                        touched: touched\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/AddWebsiteForm.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ArticleSpecificationSection__WEBPACK_IMPORTED_MODULE_9__.ArticleSpecificationSection, {\n                                        values: values,\n                                        setFieldValue: setFieldValue,\n                                        errors: errors,\n                                        touched: touched\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/AddWebsiteForm.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center gap-4 w-full\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                                type: \"button\",\n                                                variant: \"outline\",\n                                                size: \"lg\",\n                                                onClick: ()=>window.history.back(),\n                                                disabled: isSubmitting,\n                                                children: \"Cancel\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/AddWebsiteForm.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_10__.Button, {\n                                                type: \"submit\",\n                                                size: \"lg\",\n                                                disabled: isSubmitting,\n                                                className: \"bg-accentbase hover:bg-accentbase/90 text-white min-w-[120px]\",\n                                                children: isSubmitting ? isEditMode ? \"Updating...\" : \"Adding...\" : isEditMode ? \"Update Website\" : \"Add Website\"\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/AddWebsiteForm.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/AddWebsiteForm.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/AddWebsiteForm.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 15\n                            }, this);\n                        }, \"OD7bBpZva5O2jO+Puf00hKivP7c=\")\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/AddWebsiteForm.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/AddWebsiteForm.tsx\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/AddWebsiteForm.tsx\",\n        lineNumber: 171,\n        columnNumber: 5\n    }, this);\n}\n_s(AddWebsiteFormComponent, \"AHzjmCpXSNWFRHG6ZcTxjWAAx4w=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _store_formStore__WEBPACK_IMPORTED_MODULE_4__.useFormStore\n    ];\n});\n_c = AddWebsiteFormComponent;\nconst AddWebsiteForm = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(AddWebsiteFormComponent);\n_c1 = AddWebsiteForm;\nvar _c, _c1;\n$RefreshReg$(_c, \"AddWebsiteFormComponent\");\n$RefreshReg$(_c1, \"AddWebsiteForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/AddWebsiteForm.tsx\n"));

/***/ })

});
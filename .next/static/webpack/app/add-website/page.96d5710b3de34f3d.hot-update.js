"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-website/page",{

/***/ "(app-pages-browser)/./src/components/PreconditionsAlert.tsx":
/*!***********************************************!*\
  !*** ./src/components/PreconditionsAlert.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PreconditionsAlert: function() { return /* binding */ PreconditionsAlert; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronDownIcon,ChevronUpIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-up.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronDownIcon,ChevronUpIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronDownIcon,ChevronUpIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _ui_alert__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction PreconditionsAlert() {\n    _s();\n    const [isAccepted, setIsAccepted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isExpanded, setIsExpanded] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleAccept = ()=>{\n        setIsAccepted(true);\n        setIsExpanded(false);\n    };\n    const toggleExpanded = ()=>{\n        setIsExpanded(!isExpanded);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_alert__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                className: \"w-full bg-secondary-bg100 rounded-md border border-solid border-[#eaeaea] transition-all duration-200 \".concat(isExpanded ? \"rounded-b-none\" : \"\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex h-auto items-center justify-between w-full py-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_alert__WEBPACK_IMPORTED_MODULE_2__.AlertTitle, {\n                            className: \"font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foregroundbase\",\n                            children: \"Hey, Accept Preconditions before you start the listing!\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center gap-2.5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                    className: \"flex h-[31px] items-center gap-[8px] rounded-[21px] px-2.5 py-2 transition-colors \".concat(isAccepted ? \"bg-green-100 text-green-800 border-green-200\" : \"bg-orange-100 text-orange-800 border-orange-200\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 rounded-full \".concat(isAccepted ? \"bg-green-600\" : \"bg-orange-600\")\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                                            lineNumber: 40,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"font-dm-sans font-medium text-[13px] leading-[18.3px]\",\n                                            children: isAccepted ? \"Accepted\" : \"Pending\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                                            lineNumber: 45,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleExpanded,\n                                    className: \"w-5 h-5 cursor-pointer hover:text-foreground-60 transition-colors\",\n                                    children: isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                lineNumber: 22,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full bg-secondary-bg100 border border-solid border-[#eaeaea] border-t-0 rounded-b-md p-6 animate-in slide-in-from-top-2 duration-200\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-foreground-60 leading-relaxed\",\n                            children: \"Before you can proceed with your listing, please make sure to review all required preconditions. Accepting these is mandatory to continue. It ensures your submission meets our platform standards and avoids delays. Listings that don't meet these terms may be rejected. Take a moment to go through them carefully before moving ahead. Once accepted, you'll be able to start listing right away.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 13\n                        }, this),\n                        !isAccepted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex justify-start\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: handleAccept,\n                                className: \"bg-accentbase hover:bg-accentbase/90 text-white px-6 py-2 rounded-md transition-colors\",\n                                children: \"Accept\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 15\n                        }, this),\n                        isAccepted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 text-green-700\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronDownIcon_ChevronUpIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium\",\n                                    children: \"Preconditions accepted successfully!\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                                    lineNumber: 90,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                lineNumber: 65,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n_s(PreconditionsAlert, \"7q6ODOvaluO7bwf6YcShQCn3EUI=\");\n_c = PreconditionsAlert;\nvar _c;\n$RefreshReg$(_c, \"PreconditionsAlert\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1ByZWNvbmRpdGlvbnNBbGVydC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQXdDO0FBQ2lDO0FBQzFCO0FBQ1o7QUFDRTtBQUU5QixTQUFTUzs7SUFDZCxNQUFNLENBQUNDLFlBQVlDLGNBQWMsR0FBR1YsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDVyxZQUFZQyxjQUFjLEdBQUdaLCtDQUFRQSxDQUFDO0lBRTdDLE1BQU1hLGVBQWU7UUFDbkJILGNBQWM7UUFDZEUsY0FBYztJQUNoQjtJQUVBLE1BQU1FLGlCQUFpQjtRQUNyQkYsY0FBYyxDQUFDRDtJQUNqQjtJQUVBLHFCQUNFLDhEQUFDSTtRQUFJQyxXQUFVOzswQkFDYiw4REFBQ1osNENBQUtBO2dCQUNKWSxXQUFXLHlHQUVWLE9BRENMLGFBQWEsbUJBQW1COzBCQUdsQyw0RUFBQ0k7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDWCxpREFBVUE7NEJBQUNXLFdBQVU7c0NBQXdKOzs7Ozs7c0NBRzlLLDhEQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNWLDRDQUFLQTtvQ0FDSlUsV0FBVyxxRkFJVixPQUhDUCxhQUNJLGlEQUNBOztzREFJTiw4REFBQ007NENBQ0NDLFdBQVcsd0JBRVYsT0FEQ1AsYUFBYSxpQkFBaUI7Ozs7OztzREFHbEMsOERBQUNROzRDQUFLRCxXQUFVO3NEQUNiUCxhQUFhLGFBQWE7Ozs7Ozs7Ozs7Ozs4Q0FHL0IsOERBQUNTO29DQUNDQyxTQUFTTDtvQ0FDVEUsV0FBVTs4Q0FFVEwsMkJBQ0MsOERBQUNSLG1IQUFhQTt3Q0FBQ2EsV0FBVTs7Ozs7NkRBRXpCLDhEQUFDZCxtSEFBZUE7d0NBQUNjLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFRcENMLDRCQUNDLDhEQUFDSTtnQkFBSUMsV0FBVTswQkFDYiw0RUFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDSTs0QkFBRUosV0FBVTtzQ0FBNkM7Ozs7Ozt3QkFTekQsQ0FBQ1AsNEJBQ0EsOERBQUNNOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDVCw4Q0FBTUE7Z0NBQ0xZLFNBQVNOO2dDQUNURyxXQUFVOzBDQUNYOzs7Ozs7Ozs7Ozt3QkFNSlAsNEJBQ0MsOERBQUNNOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ2YsbUhBQVNBO29DQUFDZSxXQUFVOzs7Ozs7OENBQ3JCLDhEQUFDQztvQ0FBS0QsV0FBVTs4Q0FBc0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBVXREO0dBN0ZnQlI7S0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvUHJlY29uZGl0aW9uc0FsZXJ0LnRzeD9hZTI3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgQ2hlY2tJY29uLCBDaGV2cm9uRG93bkljb24sIENoZXZyb25VcEljb24gfSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XG5pbXBvcnQgeyBBbGVydCwgQWxlcnRUaXRsZSB9IGZyb20gXCIuL3VpL2FsZXJ0XCI7XG5pbXBvcnQgeyBCYWRnZSB9IGZyb20gXCIuL3VpL2JhZGdlXCI7XG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tIFwiLi91aS9idXR0b25cIjtcblxuZXhwb3J0IGZ1bmN0aW9uIFByZWNvbmRpdGlvbnNBbGVydCgpIHtcbiAgY29uc3QgW2lzQWNjZXB0ZWQsIHNldElzQWNjZXB0ZWRdID0gdXNlU3RhdGUoZmFsc2UpO1xuICBjb25zdCBbaXNFeHBhbmRlZCwgc2V0SXNFeHBhbmRlZF0gPSB1c2VTdGF0ZShmYWxzZSk7XG5cbiAgY29uc3QgaGFuZGxlQWNjZXB0ID0gKCkgPT4ge1xuICAgIHNldElzQWNjZXB0ZWQodHJ1ZSk7XG4gICAgc2V0SXNFeHBhbmRlZChmYWxzZSk7XG4gIH07XG5cbiAgY29uc3QgdG9nZ2xlRXhwYW5kZWQgPSAoKSA9PiB7XG4gICAgc2V0SXNFeHBhbmRlZCghaXNFeHBhbmRlZCk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbFwiPlxuICAgICAgPEFsZXJ0XG4gICAgICAgIGNsYXNzTmFtZT17YHctZnVsbCBiZy1zZWNvbmRhcnktYmcxMDAgcm91bmRlZC1tZCBib3JkZXIgYm9yZGVyLXNvbGlkIGJvcmRlci1bI2VhZWFlYV0gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwICR7XG4gICAgICAgICAgaXNFeHBhbmRlZCA/IFwicm91bmRlZC1iLW5vbmVcIiA6IFwiXCJcbiAgICAgICAgfWB9XG4gICAgICA+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLWF1dG8gaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB3LWZ1bGwgcHktMlwiPlxuICAgICAgICAgIDxBbGVydFRpdGxlIGNsYXNzTmFtZT1cImZvbnQtYm9keS1iNSB0ZXh0LVtsZW5ndGg6dmFyKC0tYm9keS1iNS1mb250LXNpemUpXSB0cmFja2luZy1bdmFyKC0tYm9keS1iNS1sZXR0ZXItc3BhY2luZyldIGxlYWRpbmctW3ZhcigtLWJvZHktYjUtbGluZS1oZWlnaHQpXSB0ZXh0LWZvcmVncm91bmRiYXNlXCI+XG4gICAgICAgICAgICBIZXksIEFjY2VwdCBQcmVjb25kaXRpb25zIGJlZm9yZSB5b3Ugc3RhcnQgdGhlIGxpc3RpbmchXG4gICAgICAgICAgPC9BbGVydFRpdGxlPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ2FwLTIuNVwiPlxuICAgICAgICAgICAgPEJhZGdlXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZsZXggaC1bMzFweF0gaXRlbXMtY2VudGVyIGdhcC1bOHB4XSByb3VuZGVkLVsyMXB4XSBweC0yLjUgcHktMiB0cmFuc2l0aW9uLWNvbG9ycyAke1xuICAgICAgICAgICAgICAgIGlzQWNjZXB0ZWRcbiAgICAgICAgICAgICAgICAgID8gXCJiZy1ncmVlbi0xMDAgdGV4dC1ncmVlbi04MDAgYm9yZGVyLWdyZWVuLTIwMFwiXG4gICAgICAgICAgICAgICAgICA6IFwiYmctb3JhbmdlLTEwMCB0ZXh0LW9yYW5nZS04MDAgYm9yZGVyLW9yYW5nZS0yMDBcIlxuICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgey8qIENvbG9yZWQgYnVsbGV0L2RvdCAqL31cbiAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT17YHctMiBoLTIgcm91bmRlZC1mdWxsICR7XG4gICAgICAgICAgICAgICAgICBpc0FjY2VwdGVkID8gXCJiZy1ncmVlbi02MDBcIiA6IFwiYmctb3JhbmdlLTYwMFwiXG4gICAgICAgICAgICAgICAgfWB9XG4gICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImZvbnQtZG0tc2FucyBmb250LW1lZGl1bSB0ZXh0LVsxM3B4XSBsZWFkaW5nLVsxOC4zcHhdXCI+XG4gICAgICAgICAgICAgICAge2lzQWNjZXB0ZWQgPyBcIkFjY2VwdGVkXCIgOiBcIlBlbmRpbmdcIn1cbiAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPC9CYWRnZT5cbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlRXhwYW5kZWR9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNSBoLTUgY3Vyc29yLXBvaW50ZXIgaG92ZXI6dGV4dC1mb3JlZ3JvdW5kLTYwIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICAgID5cbiAgICAgICAgICAgICAge2lzRXhwYW5kZWQgPyAoXG4gICAgICAgICAgICAgICAgPENoZXZyb25VcEljb24gY2xhc3NOYW1lPVwidy01IGgtNVwiIC8+XG4gICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgPENoZXZyb25Eb3duSWNvbiBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvQWxlcnQ+XG5cbiAgICAgIHsvKiBBY2NvcmRpb24gQ29udGVudCAqL31cbiAgICAgIHtpc0V4cGFuZGVkICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgYmctc2Vjb25kYXJ5LWJnMTAwIGJvcmRlciBib3JkZXItc29saWQgYm9yZGVyLVsjZWFlYWVhXSBib3JkZXItdC0wIHJvdW5kZWQtYi1tZCBwLTYgYW5pbWF0ZS1pbiBzbGlkZS1pbi1mcm9tLXRvcC0yIGR1cmF0aW9uLTIwMFwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS00XCI+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZm9yZWdyb3VuZC02MCBsZWFkaW5nLXJlbGF4ZWRcIj5cbiAgICAgICAgICAgICAgQmVmb3JlIHlvdSBjYW4gcHJvY2VlZCB3aXRoIHlvdXIgbGlzdGluZywgcGxlYXNlIG1ha2Ugc3VyZSB0b1xuICAgICAgICAgICAgICByZXZpZXcgYWxsIHJlcXVpcmVkIHByZWNvbmRpdGlvbnMuIEFjY2VwdGluZyB0aGVzZSBpcyBtYW5kYXRvcnkgdG9cbiAgICAgICAgICAgICAgY29udGludWUuIEl0IGVuc3VyZXMgeW91ciBzdWJtaXNzaW9uIG1lZXRzIG91ciBwbGF0Zm9ybSBzdGFuZGFyZHNcbiAgICAgICAgICAgICAgYW5kIGF2b2lkcyBkZWxheXMuIExpc3RpbmdzIHRoYXQgZG9uJ3QgbWVldCB0aGVzZSB0ZXJtcyBtYXkgYmVcbiAgICAgICAgICAgICAgcmVqZWN0ZWQuIFRha2UgYSBtb21lbnQgdG8gZ28gdGhyb3VnaCB0aGVtIGNhcmVmdWxseSBiZWZvcmUgbW92aW5nXG4gICAgICAgICAgICAgIGFoZWFkLiBPbmNlIGFjY2VwdGVkLCB5b3UnbGwgYmUgYWJsZSB0byBzdGFydCBsaXN0aW5nIHJpZ2h0IGF3YXkuXG4gICAgICAgICAgICA8L3A+XG5cbiAgICAgICAgICAgIHshaXNBY2NlcHRlZCAmJiAoXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LXN0YXJ0XCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQWNjZXB0fVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYmctYWNjZW50YmFzZSBob3ZlcjpiZy1hY2NlbnRiYXNlLzkwIHRleHQtd2hpdGUgcHgtNiBweS0yIHJvdW5kZWQtbWQgdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgIEFjY2VwdFxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgIHtpc0FjY2VwdGVkICYmIChcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiB0ZXh0LWdyZWVuLTcwMFwiPlxuICAgICAgICAgICAgICAgIDxDaGVja0ljb24gY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+XG4gICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlxuICAgICAgICAgICAgICAgICAgUHJlY29uZGl0aW9ucyBhY2NlcHRlZCBzdWNjZXNzZnVsbHkhXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cbiAgICA8L2Rpdj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwiQ2hlY2tJY29uIiwiQ2hldnJvbkRvd25JY29uIiwiQ2hldnJvblVwSWNvbiIsIkFsZXJ0IiwiQWxlcnRUaXRsZSIsIkJhZGdlIiwiQnV0dG9uIiwiUHJlY29uZGl0aW9uc0FsZXJ0IiwiaXNBY2NlcHRlZCIsInNldElzQWNjZXB0ZWQiLCJpc0V4cGFuZGVkIiwic2V0SXNFeHBhbmRlZCIsImhhbmRsZUFjY2VwdCIsInRvZ2dsZUV4cGFuZGVkIiwiZGl2IiwiY2xhc3NOYW1lIiwic3BhbiIsImJ1dHRvbiIsIm9uQ2xpY2siLCJwIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PreconditionsAlert.tsx\n"));

/***/ })

});
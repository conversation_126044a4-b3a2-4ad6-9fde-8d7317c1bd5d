"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-website/page",{

/***/ "(app-pages-browser)/./src/components/WebsiteDetailsSection.tsx":
/*!**************************************************!*\
  !*** ./src/components/WebsiteDetailsSection.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebsiteDetailsSection: function() { return /* binding */ WebsiteDetailsSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\n\n\n\n\n\nfunction WebsiteDetailsSection(param) {\n    let { values, setFieldValue, errors, touched } = param;\n    var _languageOptions_find, _languageOptions_find1, _countryOptions_find;\n    const handleCategoryChange = (categoryId, checked)=>{\n        const updatedCategories = checked ? [\n            ...values.categories,\n            categoryId\n        ] : values.categories.filter((id)=>id !== categoryId);\n        setFieldValue(\"categories\", updatedCategories);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-start gap-5 w-full shadow-shadow-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"font-heading-h3 text-[length:var(--heading-h3-font-size)] tracking-[var(--heading-h3-letter-spacing)] leading-[var(--heading-h3-line-height)] text-foregroundbase\",\n                children: \"Website detail\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"w-full p-6 bg-uicard rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-0 flex flex-col gap-[31px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-start justify-center gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col lg:flex-row items-start gap-8 w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col w-full lg:w-[264px] items-start gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                    children: \"Enter website *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    className: \"bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm\",\n                                                    placeholder: \"https://example.com\",\n                                                    value: values.websiteUrl,\n                                                    onChange: (e)=>setFieldValue(\"websiteUrl\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.websiteUrl && touched.websiteUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-errorbase\",\n                                                    children: errors.websiteUrl\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col w-full lg:w-[264px] items-start gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                    children: \"Website's Primary language *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                    value: values.primaryLanguage,\n                                                    onValueChange: (value)=>setFieldValue(\"primaryLanguage\", value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                            className: \"w-full bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[21px] h-3.5 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg\",\n                                                                            children: ((_languageOptions_find = _lib_utils__WEBPACK_IMPORTED_MODULE_7__.languageOptions.find((opt)=>opt.value === values.primaryLanguage)) === null || _languageOptions_find === void 0 ? void 0 : _languageOptions_find.flag) || \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                            lineNumber: 77,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 76,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-1 text-left\",\n                                                                        children: ((_languageOptions_find1 = _lib_utils__WEBPACK_IMPORTED_MODULE_7__.languageOptions.find((opt)=>opt.value === values.primaryLanguage)) === null || _languageOptions_find1 === void 0 ? void 0 : _languageOptions_find1.label) || \"Select language\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 83,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                lineNumber: 75,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 74,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                            children: _lib_utils__WEBPACK_IMPORTED_MODULE_7__.languageOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                    value: option.value,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg\",\n                                                                                children: option.flag\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                                lineNumber: 94,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            option.label\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 93,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, option.value, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                    lineNumber: 92,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.primaryLanguage && touched.primaryLanguage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-errorbase\",\n                                                    children: errors.primaryLanguage\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col w-full lg:w-[264px] items-start gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                    children: \"Your Majority of traffic comes from *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                    value: values.trafficCountry,\n                                                    onValueChange: (value)=>setFieldValue(\"trafficCountry\", value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                            className: \"w-full bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[21px] h-3.5 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg\",\n                                                                            children: ((_countryOptions_find = _lib_utils__WEBPACK_IMPORTED_MODULE_7__.countryOptions.find((opt)=>opt.value === values.trafficCountry)) === null || _countryOptions_find === void 0 ? void 0 : _countryOptions_find.flag) || \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                            lineNumber: 121,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 120,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                        placeholder: \"Select country\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 127,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                lineNumber: 119,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                            children: _lib_utils__WEBPACK_IMPORTED_MODULE_7__.countryOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                    value: option.value,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg\",\n                                                                                children: option.flag\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                                lineNumber: 134,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            option.label\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 133,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, option.value, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                    lineNumber: 132,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 130,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.trafficCountry && touched.trafficCountry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-errorbase\",\n                                                    children: errors.trafficCountry\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col w-full items-start gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap items-end gap-[37px_0px] w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-start\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"w-[264px] font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                    children: \"Main Category *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-0 w-full\",\n                                            children: _lib_utils__WEBPACK_IMPORTED_MODULE_7__.categories.map((column, colIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-start\",\n                                                    children: column.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex w-full lg:w-[218px] items-center justify-start gap-2 p-2 bg-white hover:bg-gray-50 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex w-6 h-6 items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                                        id: \"\".concat(category.id, \"-\").concat(colIndex, \"-\").concat(index),\n                                                                        checked: values.categories.includes(category.id),\n                                                                        onCheckedChange: (checked)=>handleCategoryChange(category.id, checked),\n                                                                        className: values.categories.includes(category.id) ? \"bg-accentbase border-accentbase\" : \"bg-white border border-solid border-[#eaeaea]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 171,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                    lineNumber: 170,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"\".concat(category.id, \"-\").concat(colIndex, \"-\").concat(index),\n                                                                    className: \"flex-1 font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foreground-60 cursor-pointer\",\n                                                                    children: category.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, \"category-\".concat(colIndex, \"-\").concat(index), true, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, \"category-column-\".concat(colIndex), false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.categories && touched.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-errorbase\",\n                                            children: errors.categories\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col w-full items-start gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                            children: \"Description of Website *\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                            className: \"h-[98px] bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm\",\n                                            placeholder: \"Describe your website, its content, audience, and what makes it unique...\",\n                                            value: values.description,\n                                            onChange: (e)=>setFieldValue(\"description\", e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.description && touched.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-errorbase\",\n                                            children: errors.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                    id: \"website-owner\",\n                                    checked: values.isOwner,\n                                    onCheckedChange: (checked)=>setFieldValue(\"isOwner\", checked),\n                                    className: \"w-4 h-4 bg-uicard border border-solid border-[#b3b3b399]\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"website-owner\",\n                                    className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase cursor-pointer\",\n                                    children: \"I am the owner of the website *\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        errors.isOwner && touched.isOwner && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-errorbase\",\n                            children: errors.isOwner\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_c = WebsiteDetailsSection;\nvar _c;\n$RefreshReg$(_c, \"WebsiteDetailsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WebsiteDetailsSection.tsx\n"));

/***/ })

});
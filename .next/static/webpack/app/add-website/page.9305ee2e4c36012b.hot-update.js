"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-website/page",{

/***/ "(app-pages-browser)/./src/components/InfoCard.tsx":
/*!*************************************!*\
  !*** ./src/components/InfoCard.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InfoCard: function() { return /* binding */ InfoCard; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n\n\n\n\n\nfunction InfoCard() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"w-full p-6 flex flex-col lg:flex-row items-center gap-8 lg:gap-[193px] bg-uicard\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col w-full lg:w-[406px] items-start gap-[17px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"font-heading-h3 text-[length:var(--heading-h3-font-size)] tracking-[var(--heading-h3-letter-spacing)] leading-[var(--heading-h3-line-height)] text-foregroundbase\",\n                        children: \"Learn how to get best out of linksera\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/InfoCard.tsx\",\n                        lineNumber: 11,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col w-full lg:w-[400px] items-start gap-2\",\n                        children: _lib_utils__WEBPACK_IMPORTED_MODULE_3__.learningPoints.map((point, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foreground-60\",\n                                children: point\n                            }, \"learning-point-\".concat(index), false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/InfoCard.tsx\",\n                                lineNumber: 16,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/InfoCard.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/InfoCard.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative w-full lg:w-[628px] h-[321px] bg-foregroundbase rounded-md overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    src: \"/header.png\",\n                    alt: \"Info Card\",\n                    fill: true,\n                    className: \"object-cover\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/InfoCard.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/InfoCard.tsx\",\n                lineNumber: 26,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/InfoCard.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, this);\n}\n_c = InfoCard;\nvar _c;\n$RefreshReg$(_c, \"InfoCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0luZm9DYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUEwQjtBQUVPO0FBQ1k7QUFDZDtBQUV4QixTQUFTSTtJQUNkLHFCQUNFLDhEQUFDSCwwQ0FBSUE7UUFBQ0ksV0FBVTs7MEJBQ2QsOERBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0U7d0JBQUdGLFdBQVU7a0NBQW9LOzs7Ozs7a0NBR2xMLDhEQUFDQzt3QkFBSUQsV0FBVTtrQ0FDWkgsc0RBQWNBLENBQUNNLEdBQUcsQ0FBQyxDQUFDQyxPQUFPQyxzQkFDMUIsOERBQUNDO2dDQUVDTixXQUFVOzBDQUVUSTsrQkFISSxrQkFBd0IsT0FBTkM7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBUy9CLDhEQUFDSjtnQkFBSUQsV0FBVTswQkFDYiw0RUFBQ0Ysa0RBQUtBO29CQUNKUyxLQUFJO29CQUNKQyxLQUFJO29CQUNKQyxJQUFJO29CQUNKVCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztBQUtwQjtLQTdCZ0JEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL0luZm9DYXJkLnRzeD9mZGRkIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcblxuaW1wb3J0IHsgQ2FyZCB9IGZyb20gXCIuL3VpL2NhcmRcIjtcbmltcG9ydCB7IGxlYXJuaW5nUG9pbnRzIH0gZnJvbSBcIkAvbGliL3V0aWxzXCI7XG5pbXBvcnQgSW1hZ2UgZnJvbSBcIm5leHQvaW1hZ2VcIjtcblxuZXhwb3J0IGZ1bmN0aW9uIEluZm9DYXJkKCkge1xuICByZXR1cm4gKFxuICAgIDxDYXJkIGNsYXNzTmFtZT1cInctZnVsbCBwLTYgZmxleCBmbGV4LWNvbCBsZzpmbGV4LXJvdyBpdGVtcy1jZW50ZXIgZ2FwLTggbGc6Z2FwLVsxOTNweF0gYmctdWljYXJkXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgdy1mdWxsIGxnOnctWzQwNnB4XSBpdGVtcy1zdGFydCBnYXAtWzE3cHhdXCI+XG4gICAgICAgIDxoMiBjbGFzc05hbWU9XCJmb250LWhlYWRpbmctaDMgdGV4dC1bbGVuZ3RoOnZhcigtLWhlYWRpbmctaDMtZm9udC1zaXplKV0gdHJhY2tpbmctW3ZhcigtLWhlYWRpbmctaDMtbGV0dGVyLXNwYWNpbmcpXSBsZWFkaW5nLVt2YXIoLS1oZWFkaW5nLWgzLWxpbmUtaGVpZ2h0KV0gdGV4dC1mb3JlZ3JvdW5kYmFzZVwiPlxuICAgICAgICAgIExlYXJuIGhvdyB0byBnZXQgYmVzdCBvdXQgb2YgbGlua3NlcmFcbiAgICAgICAgPC9oMj5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIHctZnVsbCBsZzp3LVs0MDBweF0gaXRlbXMtc3RhcnQgZ2FwLTJcIj5cbiAgICAgICAgICB7bGVhcm5pbmdQb2ludHMubWFwKChwb2ludCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgIDxsaVxuICAgICAgICAgICAgICBrZXk9e2BsZWFybmluZy1wb2ludC0ke2luZGV4fWB9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZvbnQtYm9keS1iNSB0ZXh0LVtsZW5ndGg6dmFyKC0tYm9keS1iNS1mb250LXNpemUpXSB0cmFja2luZy1bdmFyKC0tYm9keS1iNS1sZXR0ZXItc3BhY2luZyldIGxlYWRpbmctW3ZhcigtLWJvZHktYjUtbGluZS1oZWlnaHQpXSB0ZXh0LWZvcmVncm91bmQtNjBcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7cG9pbnR9XG4gICAgICAgICAgICA8L2xpPlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlIHctZnVsbCBsZzp3LVs2MjhweF0gaC1bMzIxcHhdIGJnLWZvcmVncm91bmRiYXNlIHJvdW5kZWQtbWQgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgIDxJbWFnZVxuICAgICAgICAgIHNyYz1cIi9oZWFkZXIucG5nXCJcbiAgICAgICAgICBhbHQ9XCJJbmZvIENhcmRcIlxuICAgICAgICAgIGZpbGxcbiAgICAgICAgICBjbGFzc05hbWU9XCJvYmplY3QtY292ZXJcIlxuICAgICAgICAvPlxuICAgICAgPC9kaXY+XG4gICAgPC9DYXJkPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQ2FyZCIsImxlYXJuaW5nUG9pbnRzIiwiSW1hZ2UiLCJJbmZvQ2FyZCIsImNsYXNzTmFtZSIsImRpdiIsImgyIiwibWFwIiwicG9pbnQiLCJpbmRleCIsImxpIiwic3JjIiwiYWx0IiwiZmlsbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/InfoCard.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-website/page",{

/***/ "(app-pages-browser)/./src/components/PreconditionsAlert.tsx":
/*!***********************************************!*\
  !*** ./src/components/PreconditionsAlert.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PreconditionsAlert: function() { return /* binding */ PreconditionsAlert; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronDownIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronDownIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_CheckIcon_ChevronDownIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckIcon,ChevronDownIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _ui_alert__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/alert */ \"(app-pages-browser)/./src/components/ui/alert.tsx\");\n/* harmony import */ var _ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n\n\n\n\n\nfunction PreconditionsAlert() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_alert__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n        className: \"w-full h-14 bg-secondary-bg100 rounded-md border border-solid border-[#eaeaea]\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-auto items-center justify-between w-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_alert__WEBPACK_IMPORTED_MODULE_2__.AlertTitle, {\n                    className: \"font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foregroundbase\",\n                    children: \"Hey, Accept Preconditions before you start the listing!\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center gap-2.5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                            className: \"flex h-[31px] items-center gap-[11px] bg-success-10 rounded-[21px] px-2.5 py-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronDownIcon_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-dm-sans font-medium text-foregroundbase text-[13px] leading-[18.3px]\",\n                                    children: \"Accepted\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckIcon_ChevronDownIcon_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"w-5 h-5 cursor-pointer hover:text-foreground-60 transition-colors\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                            lineNumber: 20,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n                    lineNumber: 13,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n            lineNumber: 9,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/PreconditionsAlert.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n_c = PreconditionsAlert;\nvar _c;\n$RefreshReg$(_c, \"PreconditionsAlert\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PreconditionsAlert.tsx\n"));

/***/ })

});
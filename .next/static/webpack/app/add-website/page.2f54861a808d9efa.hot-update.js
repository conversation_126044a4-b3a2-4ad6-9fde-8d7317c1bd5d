"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-website/page",{

/***/ "(app-pages-browser)/./src/components/WebsiteDetailsSection.tsx":
/*!**************************************************!*\
  !*** ./src/components/WebsiteDetailsSection.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebsiteDetailsSection: function() { return /* binding */ WebsiteDetailsSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\n\n\n\n\n\nfunction WebsiteDetailsSection(param) {\n    let { values, setFieldValue, errors, touched } = param;\n    var _languageOptions_find, _countryOptions_find;\n    const handleCategoryChange = (categoryId, checked)=>{\n        const updatedCategories = checked ? [\n            ...values.categories,\n            categoryId\n        ] : values.categories.filter((id)=>id !== categoryId);\n        setFieldValue(\"categories\", updatedCategories);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-start gap-5 w-full shadow-shadow-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"font-heading-h3 text-[length:var(--heading-h3-font-size)] tracking-[var(--heading-h3-letter-spacing)] leading-[var(--heading-h3-line-height)] text-foregroundbase\",\n                children: \"Website detail\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"w-full p-6 bg-uicard rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-0 flex flex-col gap-[31px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-start justify-center gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col lg:flex-row items-start gap-8 w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col w-full lg:w-[264px] items-start gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                    children: \"Enter website *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    className: \"bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm\",\n                                                    placeholder: \"https://example.com\",\n                                                    value: values.websiteUrl,\n                                                    onChange: (e)=>setFieldValue(\"websiteUrl\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.websiteUrl && touched.websiteUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-errorbase\",\n                                                    children: errors.websiteUrl\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col w-full lg:w-[264px] items-start gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                    children: \"Website's Primary language *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                    value: values.primaryLanguage,\n                                                    onValueChange: (value)=>setFieldValue(\"primaryLanguage\", value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                            className: \"w-full bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[21px] h-3.5 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg\",\n                                                                            children: ((_languageOptions_find = _lib_utils__WEBPACK_IMPORTED_MODULE_7__.languageOptions.find((opt)=>opt.value === values.primaryLanguage)) === null || _languageOptions_find === void 0 ? void 0 : _languageOptions_find.flag) || \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                            lineNumber: 77,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 76,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                        placeholder: \"Select language\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 83,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                lineNumber: 75,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 74,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                            children: _lib_utils__WEBPACK_IMPORTED_MODULE_7__.languageOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                    value: option.value,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg\",\n                                                                                children: option.flag\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                                lineNumber: 90,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            option.label\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 89,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, option.value, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                    lineNumber: 88,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 86,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.primaryLanguage && touched.primaryLanguage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-errorbase\",\n                                                    children: errors.primaryLanguage\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col w-full lg:w-[264px] items-start gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                    children: \"Your Majority of traffic comes from *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                    value: values.trafficCountry,\n                                                    onValueChange: (value)=>setFieldValue(\"trafficCountry\", value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                            className: \"w-full bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[21px] h-3.5 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg\",\n                                                                            children: ((_countryOptions_find = _lib_utils__WEBPACK_IMPORTED_MODULE_7__.countryOptions.find((opt)=>opt.value === values.trafficCountry)) === null || _countryOptions_find === void 0 ? void 0 : _countryOptions_find.flag) || \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                            lineNumber: 117,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 116,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                        placeholder: \"Select country\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 123,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                lineNumber: 115,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                            children: _lib_utils__WEBPACK_IMPORTED_MODULE_7__.countryOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                    value: option.value,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                className: \"w-[19px] h-[13px]\",\n                                                                                alt: \"Flag\",\n                                                                                src: option.flag\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                                lineNumber: 130,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            option.label\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 129,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, option.value, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                    lineNumber: 128,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.trafficCountry && touched.trafficCountry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-errorbase\",\n                                                    children: errors.trafficCountry\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col w-full items-start gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap items-end gap-[37px_0px] w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-start\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"w-[264px] font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                    children: \"Main Category *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-0 w-full\",\n                                            children: _lib_utils__WEBPACK_IMPORTED_MODULE_7__.categories.map((column, colIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-start\",\n                                                    children: column.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex w-full lg:w-[218px] items-center justify-start gap-2 p-2 bg-white hover:bg-gray-50 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex w-6 h-6 items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                                        id: \"\".concat(category.id, \"-\").concat(colIndex, \"-\").concat(index),\n                                                                        checked: values.categories.includes(category.id),\n                                                                        onCheckedChange: (checked)=>handleCategoryChange(category.id, checked),\n                                                                        className: values.categories.includes(category.id) ? \"bg-accentbase border-accentbase\" : \"bg-white border border-solid border-[#eaeaea]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 171,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                    lineNumber: 170,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"\".concat(category.id, \"-\").concat(colIndex, \"-\").concat(index),\n                                                                    className: \"flex-1 font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foreground-60 cursor-pointer\",\n                                                                    children: category.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                    lineNumber: 187,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, \"category-\".concat(colIndex, \"-\").concat(index), true, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, \"category-column-\".concat(colIndex), false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 161,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.categories && touched.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-errorbase\",\n                                            children: errors.categories\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col w-full items-start gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                            children: \"Description of Website *\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 207,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                            className: \"h-[98px] bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm\",\n                                            placeholder: \"Describe your website, its content, audience, and what makes it unique...\",\n                                            value: values.description,\n                                            onChange: (e)=>setFieldValue(\"description\", e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.description && touched.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-errorbase\",\n                                            children: errors.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                    id: \"website-owner\",\n                                    checked: values.isOwner,\n                                    onCheckedChange: (checked)=>setFieldValue(\"isOwner\", checked),\n                                    className: \"w-4 h-4 bg-uicard border border-solid border-[#b3b3b399]\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"website-owner\",\n                                    className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase cursor-pointer\",\n                                    children: \"I am the owner of the website *\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 232,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this),\n                        errors.isOwner && touched.isOwner && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-errorbase\",\n                            children: errors.isOwner\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                            lineNumber: 240,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_c = WebsiteDetailsSection;\nvar _c;\n$RefreshReg$(_c, \"WebsiteDetailsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WebsiteDetailsSection.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-website/page",{

/***/ "(app-pages-browser)/./src/app/add-website/page.tsx":
/*!**************************************!*\
  !*** ./src/app/add-website/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_AddWebsiteForm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/AddWebsiteForm */ \"(app-pages-browser)/./src/components/AddWebsiteForm.tsx\");\n/* harmony import */ var _store_formStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/formStore */ \"(app-pages-browser)/./src/store/formStore.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst AddWebsitePage = ()=>{\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const { loadWebsiteForEdit, resetForm, editingWebsiteId } = (0,_store_formStore__WEBPACK_IMPORTED_MODULE_2__.useFormStore)();\n    const editId = searchParams.get(\"edit\");\n    const isEditMode = Boolean(editId);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (editId) {\n            // Always ensure we have the correct data loaded for the edit ID\n            // This handles both initial navigation and page refresh scenarios\n            if (editId !== editingWebsiteId) {\n                loadWebsiteForEdit(editId);\n            }\n        } else if (!editId && editingWebsiteId) {\n            // Reset form when switching from edit to create mode\n            resetForm();\n        }\n    }, [\n        editId,\n        editingWebsiteId,\n        loadWebsiteForEdit,\n        resetForm\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-background-25\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AddWebsiteForm__WEBPACK_IMPORTED_MODULE_1__.AddWebsiteForm, {\n            isEditMode: isEditMode\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/app/add-website/page.tsx\",\n            lineNumber: 31,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/app/add-website/page.tsx\",\n        lineNumber: 30,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AddWebsitePage, \"ZxpkzCqlsR0gh/xXMSr/7AlHCe4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter,\n        _store_formStore__WEBPACK_IMPORTED_MODULE_2__.useFormStore\n    ];\n});\n_c = AddWebsitePage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddWebsitePage);\nvar _c;\n$RefreshReg$(_c, \"AddWebsitePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/add-website/page.tsx\n"));

/***/ })

});
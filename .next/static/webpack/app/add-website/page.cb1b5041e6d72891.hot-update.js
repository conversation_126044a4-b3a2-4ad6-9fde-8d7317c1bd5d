"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-website/page",{

/***/ "(app-pages-browser)/./src/components/WebsiteDetailsSection.tsx":
/*!**************************************************!*\
  !*** ./src/components/WebsiteDetailsSection.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebsiteDetailsSection: function() { return /* binding */ WebsiteDetailsSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\n\n\n\n\n\nfunction WebsiteDetailsSection(param) {\n    let { values, setFieldValue, errors, touched } = param;\n    var _languageOptions_find, _languageOptions_find1, _countryOptions_find, _countryOptions_find1;\n    const handleCategoryChange = (categoryId, checked)=>{\n        const updatedCategories = checked ? [\n            ...values.categories,\n            categoryId\n        ] : values.categories.filter((id)=>id !== categoryId);\n        setFieldValue(\"categories\", updatedCategories);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-start gap-5 w-full shadow-shadow-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"font-heading-h3 text-[length:var(--heading-h3-font-size)] tracking-[var(--heading-h3-letter-spacing)] leading-[var(--heading-h3-line-height)] text-foregroundbase\",\n                children: \"Website detail\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"w-full p-6 bg-uicard rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-0 flex flex-col gap-[31px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-start justify-center gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col lg:flex-row items-start gap-8 w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col w-full lg:w-[264px] items-start gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                    children: \"Enter website *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    className: \"bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm\",\n                                                    placeholder: \"https://example.com\",\n                                                    value: values.websiteUrl,\n                                                    onChange: (e)=>setFieldValue(\"websiteUrl\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.websiteUrl && touched.websiteUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-errorbase\",\n                                                    children: errors.websiteUrl\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col w-full lg:w-[264px] items-start gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                    children: \"Website's Primary language *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                    value: values.primaryLanguage,\n                                                    onValueChange: (value)=>setFieldValue(\"primaryLanguage\", value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                            className: \"w-full bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[21px] h-3.5 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg\",\n                                                                            children: ((_languageOptions_find = _lib_utils__WEBPACK_IMPORTED_MODULE_7__.languageOptions.find((opt)=>opt.value === values.primaryLanguage)) === null || _languageOptions_find === void 0 ? void 0 : _languageOptions_find.flag) || \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                            lineNumber: 77,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 76,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-1 text-left\",\n                                                                        children: ((_languageOptions_find1 = _lib_utils__WEBPACK_IMPORTED_MODULE_7__.languageOptions.find((opt)=>opt.value === values.primaryLanguage)) === null || _languageOptions_find1 === void 0 ? void 0 : _languageOptions_find1.label) || \"Select language\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 83,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                lineNumber: 75,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 74,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                            children: _lib_utils__WEBPACK_IMPORTED_MODULE_7__.languageOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                    value: option.value,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg\",\n                                                                                children: option.flag\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                                lineNumber: 94,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            option.label\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 93,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, option.value, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                    lineNumber: 92,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.primaryLanguage && touched.primaryLanguage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-errorbase\",\n                                                    children: errors.primaryLanguage\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col w-full lg:w-[264px] items-start gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                    children: \"Your Majority of traffic comes from *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 109,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                    value: values.trafficCountry,\n                                                    onValueChange: (value)=>setFieldValue(\"trafficCountry\", value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                            className: \"w-full bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[21px] h-3.5 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg\",\n                                                                            children: ((_countryOptions_find = _lib_utils__WEBPACK_IMPORTED_MODULE_7__.countryOptions.find((opt)=>opt.value === values.trafficCountry)) === null || _countryOptions_find === void 0 ? void 0 : _countryOptions_find.flag) || \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                            lineNumber: 121,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 120,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"flex-1 text-left\",\n                                                                        children: ((_countryOptions_find1 = _lib_utils__WEBPACK_IMPORTED_MODULE_7__.countryOptions.find((opt)=>opt.value === values.trafficCountry)) === null || _countryOptions_find1 === void 0 ? void 0 : _countryOptions_find1.label) || \"Select country\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 127,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                lineNumber: 119,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 118,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                            children: _lib_utils__WEBPACK_IMPORTED_MODULE_7__.countryOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                    value: option.value,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg\",\n                                                                                children: option.flag\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                                lineNumber: 138,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            option.label\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 137,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, option.value, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                    lineNumber: 136,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 134,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 112,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.trafficCountry && touched.trafficCountry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-errorbase\",\n                                                    children: errors.trafficCountry\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col w-full items-start gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap items-end gap-[37px_0px] w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-start\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"w-[264px] font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                    children: \"Main Category *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-0 w-full\",\n                                            children: _lib_utils__WEBPACK_IMPORTED_MODULE_7__.categories.map((column, colIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-start\",\n                                                    children: column.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex w-full lg:w-[218px] items-center justify-start gap-2 p-2 bg-white hover:bg-gray-50 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex w-6 h-6 items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                                        id: \"\".concat(category.id, \"-\").concat(colIndex, \"-\").concat(index),\n                                                                        checked: values.categories.includes(category.id),\n                                                                        onCheckedChange: (checked)=>handleCategoryChange(category.id, checked),\n                                                                        className: values.categories.includes(category.id) ? \"bg-accentbase border-accentbase\" : \"bg-white border border-solid border-[#eaeaea]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 175,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                    lineNumber: 174,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"\".concat(category.id, \"-\").concat(colIndex, \"-\").concat(index),\n                                                                    className: \"flex-1 font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foreground-60 cursor-pointer\",\n                                                                    children: category.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                    lineNumber: 191,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, \"category-\".concat(colIndex, \"-\").concat(index), true, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 170,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, \"category-column-\".concat(colIndex), false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 165,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.categories && touched.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-errorbase\",\n                                            children: errors.categories\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col w-full items-start gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                            children: \"Description of Website *\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                            className: \"h-[98px] bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm\",\n                                            placeholder: \"Describe your website, its content, audience, and what makes it unique...\",\n                                            value: values.description,\n                                            onChange: (e)=>setFieldValue(\"description\", e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.description && touched.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-errorbase\",\n                                            children: errors.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                    id: \"website-owner\",\n                                    checked: values.isOwner,\n                                    onCheckedChange: (checked)=>setFieldValue(\"isOwner\", checked),\n                                    className: \"w-4 h-4 bg-uicard border border-solid border-[#b3b3b399]\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"website-owner\",\n                                    className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase cursor-pointer\",\n                                    children: \"I am the owner of the website *\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 11\n                        }, this),\n                        errors.isOwner && touched.isOwner && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-errorbase\",\n                            children: errors.isOwner\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                            lineNumber: 244,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_c = WebsiteDetailsSection;\nvar _c;\n$RefreshReg$(_c, \"WebsiteDetailsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WebsiteDetailsSection.tsx\n"));

/***/ })

});
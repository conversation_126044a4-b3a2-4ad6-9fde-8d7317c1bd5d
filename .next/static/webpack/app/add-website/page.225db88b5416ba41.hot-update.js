"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-website/page",{

/***/ "(app-pages-browser)/./src/components/WebsiteDetailsSection.tsx":
/*!**************************************************!*\
  !*** ./src/components/WebsiteDetailsSection.tsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WebsiteDetailsSection: function() { return /* binding */ WebsiteDetailsSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/checkbox */ \"(app-pages-browser)/./src/components/ui/checkbox.tsx\");\n/* harmony import */ var _ui_select__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./ui/select */ \"(app-pages-browser)/./src/components/ui/select.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\n\n\n\n\n\nfunction WebsiteDetailsSection(param) {\n    let { values, setFieldValue, errors, touched } = param;\n    var _languageOptions_find, _countryOptions_find;\n    const handleCategoryChange = (categoryId, checked)=>{\n        const updatedCategories = checked ? [\n            ...values.categories,\n            categoryId\n        ] : values.categories.filter((id)=>id !== categoryId);\n        setFieldValue(\"categories\", updatedCategories);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-start gap-5 w-full shadow-shadow-sm\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"font-heading-h3 text-[length:var(--heading-h3-font-size)] tracking-[var(--heading-h3-letter-spacing)] leading-[var(--heading-h3-line-height)] text-foregroundbase\",\n                children: \"Website detail\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                lineNumber: 39,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"w-full p-6 bg-uicard rounded-md\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-0 flex flex-col gap-[31px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col items-start justify-center gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col lg:flex-row items-start gap-8 w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col w-full lg:w-[264px] items-start gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                    children: \"Enter website *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                    className: \"bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm\",\n                                                    placeholder: \"https://example.com\",\n                                                    value: values.websiteUrl,\n                                                    onChange: (e)=>setFieldValue(\"websiteUrl\", e.target.value)\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.websiteUrl && touched.websiteUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-errorbase\",\n                                                    children: errors.websiteUrl\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 47,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col w-full lg:w-[264px] items-start gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                    children: \"Website's Primary language *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 65,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                    value: values.primaryLanguage,\n                                                    onValueChange: (value)=>setFieldValue(\"primaryLanguage\", value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                            className: \"w-full bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[21px] h-3.5 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg\",\n                                                                            children: ((_languageOptions_find = _lib_utils__WEBPACK_IMPORTED_MODULE_7__.languageOptions.find((opt)=>opt.value === values.primaryLanguage)) === null || _languageOptions_find === void 0 ? void 0 : _languageOptions_find.flag) || \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                            lineNumber: 77,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 76,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                        placeholder: \"Select language\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 83,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                lineNumber: 75,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 74,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                            children: _lib_utils__WEBPACK_IMPORTED_MODULE_7__.languageOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                    value: option.value,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg\",\n                                                                                children: option.flag\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                                lineNumber: 90,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            option.label\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 89,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, option.value, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                    lineNumber: 88,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 86,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 68,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.primaryLanguage && touched.primaryLanguage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-errorbase\",\n                                                    children: errors.primaryLanguage\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 64,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col w-full lg:w-[264px] items-start gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                    children: \"Your Majority of traffic comes from *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.Select, {\n                                                    value: values.trafficCountry,\n                                                    onValueChange: (value)=>setFieldValue(\"trafficCountry\", value),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectTrigger, {\n                                                            className: \"w-full bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-[21px] h-3.5 flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-lg\",\n                                                                            children: ((_countryOptions_find = _lib_utils__WEBPACK_IMPORTED_MODULE_7__.countryOptions.find((opt)=>opt.value === values.trafficCountry)) === null || _countryOptions_find === void 0 ? void 0 : _countryOptions_find.flag) || \"\\uD83C\\uDDFA\\uD83C\\uDDF8\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                            lineNumber: 117,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 116,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectValue, {\n                                                                        placeholder: \"Select country\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 123,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                lineNumber: 115,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 114,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectContent, {\n                                                            children: _lib_utils__WEBPACK_IMPORTED_MODULE_7__.countryOptions.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_select__WEBPACK_IMPORTED_MODULE_6__.SelectItem, {\n                                                                    value: option.value,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-lg\",\n                                                                                children: option.flag\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                                lineNumber: 130,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            option.label\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 129,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, option.value, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                    lineNumber: 128,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 126,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 17\n                                                }, this),\n                                                errors.trafficCountry && touched.trafficCountry && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-errorbase\",\n                                                    children: errors.trafficCountry\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 138,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 104,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 46,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col w-full items-start gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap items-end gap-[37px_0px] w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-start\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    className: \"w-[264px] font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                    children: \"Main Category *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 149,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-0 w-full\",\n                                            children: _lib_utils__WEBPACK_IMPORTED_MODULE_7__.categories.map((column, colIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-start\",\n                                                    children: column.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex w-full lg:w-[218px] items-center justify-start gap-2 p-2 bg-white hover:bg-gray-50 transition-colors\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex w-6 h-6 items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                                                        id: \"\".concat(category.id, \"-\").concat(colIndex, \"-\").concat(index),\n                                                                        checked: values.categories.includes(category.id),\n                                                                        onCheckedChange: (checked)=>handleCategoryChange(category.id, checked),\n                                                                        className: values.categories.includes(category.id) ? \"bg-accentbase border-accentbase\" : \"bg-white border border-solid border-[#eaeaea]\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                        lineNumber: 167,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                    lineNumber: 166,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                    htmlFor: \"\".concat(category.id, \"-\").concat(colIndex, \"-\").concat(index),\n                                                                    className: \"flex-1 font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foreground-60 cursor-pointer\",\n                                                                    children: category.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                                    lineNumber: 183,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, \"category-\".concat(colIndex, \"-\").concat(index), true, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                            lineNumber: 162,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, \"category-column-\".concat(colIndex), false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 155,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.categories && touched.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-errorbase\",\n                                            children: errors.categories\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col w-full items-start gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                            children: \"Description of Website *\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                            className: \"h-[98px] bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm\",\n                                            placeholder: \"Describe your website, its content, audience, and what makes it unique...\",\n                                            value: values.description,\n                                            onChange: (e)=>setFieldValue(\"description\", e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this),\n                                        errors.description && touched.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-errorbase\",\n                                            children: errors.description\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                                    id: \"website-owner\",\n                                    checked: values.isOwner,\n                                    onCheckedChange: (checked)=>setFieldValue(\"isOwner\", checked),\n                                    className: \"w-4 h-4 bg-uicard border border-solid border-[#b3b3b399]\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    htmlFor: \"website-owner\",\n                                    className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase cursor-pointer\",\n                                    children: \"I am the owner of the website *\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        errors.isOwner && touched.isOwner && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-sm text-errorbase\",\n                            children: errors.isOwner\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                            lineNumber: 236,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                    lineNumber: 43,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n                lineNumber: 42,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/WebsiteDetailsSection.tsx\",\n        lineNumber: 38,\n        columnNumber: 5\n    }, this);\n}\n_c = WebsiteDetailsSection;\nvar _c;\n$RefreshReg$(_c, \"WebsiteDetailsSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/WebsiteDetailsSection.tsx\n"));

/***/ })

});
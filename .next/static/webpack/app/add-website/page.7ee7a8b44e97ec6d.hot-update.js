"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-website/page",{

/***/ "(app-pages-browser)/./src/components/CreateOfferSection.tsx":
/*!***********************************************!*\
  !*** ./src/components/CreateOfferSection.tsx ***!
  \***********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CreateOfferSection: function() { return /* binding */ CreateOfferSection; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _ui_textarea__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n\n\n\n\n\n\nfunction CreateOfferSection(param) {\n    let { values, setFieldValue, errors, touched } = param;\n    var _errors_homepageOffer, _touched_homepageOffer, _errors_homepageOffer1, _touched_homepageOffer1, _values_homepageOffer_description;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-start gap-5 w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"font-heading-h3 text-[length:var(--heading-h3-font-size)] tracking-[var(--heading-h3-letter-spacing)] leading-[var(--heading-h3-line-height)] text-foregroundbase\",\n                children: \"Create offer\"\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                className: \"p-6 bg-[#ffffff] rounded-lg shadow-shadow-sm w-full\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                    className: \"p-0 flex flex-col gap-12\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                        defaultValue: \"normal\",\n                        className: \"w-full\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                                className: \"flex gap-12 border-b border-[#eaeaea] w-full justify-start h-12 bg-transparent p-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                        value: \"normal\",\n                                        className: \"font-heading-h5 text-[length:var(--heading-h5-font-size)] tracking-[var(--heading-h5-letter-spacing)] leading-[var(--heading-h5-line-height)] data-[state=active]:text-foregroundbase data-[state=active]:border-b-2 data-[state=active]:border-[#613fdd] data-[state=inactive]:text-foreground-40 rounded-none px-2.5 py-2.5\",\n                                        children: \"Normal offer\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                        lineNumber: 39,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                        value: \"grey\",\n                                        className: \"font-heading-h5 text-[length:var(--heading-h5-font-size)] tracking-[var(--heading-h5-letter-spacing)] leading-[var(--heading-h5-line-height)] data-[state=active]:text-foregroundbase data-[state=active]:border-b-2 data-[state=active]:border-[#613fdd] data-[state=inactive]:text-foreground-40 rounded-none px-2.5 py-2.5\",\n                                        children: \"Grey Niche offer\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                        lineNumber: 45,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                        value: \"homepage\",\n                                        className: \"font-heading-h5 text-[length:var(--heading-h5-font-size)] tracking-[var(--heading-h5-letter-spacing)] leading-[var(--heading-h5-line-height)] data-[state=active]:text-foregroundbase data-[state=active]:border-b-2 data-[state=active]:border-[#613fdd] data-[state=inactive]:text-foreground-40 rounded-none px-2.5 py-2.5\",\n                                        children: \"Homepage link\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                lineNumber: 38,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                value: \"normal\",\n                                className: \"mt-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col md:flex-row items-start gap-8 w-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-start gap-5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col w-full md:w-[262px] items-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                            children: \"Guest posting *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                            lineNumber: 64,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex w-full border border-solid border-[#eaeaea] rounded-md\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex w-10 h-10 items-center justify-center bg-secondary-bg100 rounded-[6px_0px_0px_6px] border border-solid border-[#eaeaea]\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-text-md-normal text-[length:var(--text-md-normal-font-size)] tracking-[var(--text-md-normal-letter-spacing)] leading-[var(--text-md-normal-line-height)] text-mutedbase text-center\",\n                                                                        children: \"$\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                        lineNumber: 69,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                    lineNumber: 68,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                    className: \"w-full md:w-[222px] h-10 bg-secondary-bg100 rounded-[0px_6px_6px_0px] border-t border-r border-b border-[#eaeaea]\",\n                                                                    type: \"number\",\n                                                                    value: values.guestPostingPrice,\n                                                                    onChange: (e)=>setFieldValue(\"guestPostingPrice\", Number(e.target.value)),\n                                                                    min: \"1\",\n                                                                    max: \"10000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                    lineNumber: 73,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                            lineNumber: 67,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        errors.guestPostingPrice && touched.guestPostingPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-errorbase\",\n                                                            children: errors.guestPostingPrice\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                            lineNumber: 89,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                lineNumber: 62,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-start gap-5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-8\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col w-full md:w-[262px] items-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                            children: \"Link insertion *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                            lineNumber: 100,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex w-full border border-solid border-[#eaeaea] rounded-md\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex w-10 h-10 items-center justify-center bg-secondary-bg100 rounded-[6px_0px_0px_6px] border border-solid border-[#eaeaea]\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-text-md-normal text-[length:var(--text-md-normal-font-size)] tracking-[var(--text-md-normal-letter-spacing)] leading-[var(--text-md-normal-line-height)] text-mutedbase text-center\",\n                                                                        children: \"$\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                        lineNumber: 105,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                    lineNumber: 104,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                    className: \"w-full md:w-[222px] h-10 bg-secondary-bg100 rounded-[0px_6px_6px_0px] border-t border-r border-b border-[#eaeaea]\",\n                                                                    type: \"number\",\n                                                                    value: values.linkInsertionPrice,\n                                                                    onChange: (e)=>setFieldValue(\"linkInsertionPrice\", Number(e.target.value)),\n                                                                    min: \"1\",\n                                                                    max: \"10000\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                    lineNumber: 109,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                            lineNumber: 103,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        errors.linkInsertionPrice && touched.linkInsertionPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-errorbase\",\n                                                            children: errors.linkInsertionPrice\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                            lineNumber: 125,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                    lineNumber: 60,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                value: \"grey\",\n                                className: \"mt-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-foreground-60 text-sm mb-6\",\n                                            children: \"Set pricing for grey niche categories. Leave at $0 if you don't accept that niche.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                                            children: Object.entries({\n                                                casino: {\n                                                    label: \"Casino & Gambling\",\n                                                    color: \"text-red-600\"\n                                                },\n                                                cbd: {\n                                                    label: \"CBD & Cannabis\",\n                                                    color: \"text-green-600\"\n                                                },\n                                                crypto: {\n                                                    label: \"Cryptocurrency\",\n                                                    color: \"text-orange-600\"\n                                                },\n                                                forex: {\n                                                    label: \"Forex Trading\",\n                                                    color: \"text-blue-600\"\n                                                },\n                                                adult: {\n                                                    label: \"Adult Content\",\n                                                    color: \"text-pink-600\"\n                                                },\n                                                vaping: {\n                                                    label: \"Vaping & E-cigarettes\",\n                                                    color: \"text-purple-600\"\n                                                }\n                                            }).map((param)=>{\n                                                let [niche, config] = param;\n                                                var _values_greyNicheOffers_niche, _errors_greyNicheOffers_niche, _errors_greyNicheOffers, _errors_greyNicheOffers_niche1, _values_greyNicheOffers_niche1, _errors_greyNicheOffers_niche2, _errors_greyNicheOffers1, _errors_greyNicheOffers_niche3;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                    className: \"p-4 border border-gray-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        className: \"p-0 space-y-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center gap-3\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-foregroundbase\",\n                                                                    children: config.label\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-3\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-col gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"text-sm font-medium text-foregroundbase\",\n                                                                                children: \"Guest Post Price\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                                lineNumber: 181,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex border border-solid border-[#eaeaea] rounded-md\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex w-8 h-9 items-center justify-center bg-secondary-bg100 rounded-[6px_0px_0px_6px] border-r border-[#eaeaea]\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-sm text-mutedbase\",\n                                                                                            children: \"$\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                                            lineNumber: 186,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                                        lineNumber: 185,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                        className: \"h-9 bg-secondary-bg100 rounded-[0px_6px_6px_0px] border-0\",\n                                                                                        type: \"number\",\n                                                                                        value: ((_values_greyNicheOffers_niche = values.greyNicheOffers[niche]) === null || _values_greyNicheOffers_niche === void 0 ? void 0 : _values_greyNicheOffers_niche.guestPostPrice) || 0,\n                                                                                        onChange: (e)=>setFieldValue(\"greyNicheOffers.\".concat(niche, \".guestPostPrice\"), Number(e.target.value)),\n                                                                                        min: \"0\",\n                                                                                        max: \"10000\",\n                                                                                        placeholder: \"0\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                                        lineNumber: 190,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                                lineNumber: 184,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            ((_errors_greyNicheOffers = errors.greyNicheOffers) === null || _errors_greyNicheOffers === void 0 ? void 0 : (_errors_greyNicheOffers_niche = _errors_greyNicheOffers[niche]) === null || _errors_greyNicheOffers_niche === void 0 ? void 0 : _errors_greyNicheOffers_niche.guestPostPrice) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-errorbase\",\n                                                                                children: (_errors_greyNicheOffers_niche1 = errors.greyNicheOffers[niche]) === null || _errors_greyNicheOffers_niche1 === void 0 ? void 0 : _errors_greyNicheOffers_niche1.guestPostPrice\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                                lineNumber: 212,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                        lineNumber: 180,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex flex-col gap-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                className: \"text-sm font-medium text-foregroundbase\",\n                                                                                children: \"Link Insertion Price\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                                lineNumber: 224,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex border border-solid border-[#eaeaea] rounded-md\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex w-8 h-9 items-center justify-center bg-secondary-bg100 rounded-[6px_0px_0px_6px] border-r border-[#eaeaea]\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"text-sm text-mutedbase\",\n                                                                                            children: \"$\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                                            lineNumber: 229,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                                        lineNumber: 228,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                                        className: \"h-9 bg-secondary-bg100 rounded-[0px_6px_6px_0px] border-0\",\n                                                                                        type: \"number\",\n                                                                                        value: ((_values_greyNicheOffers_niche1 = values.greyNicheOffers[niche]) === null || _values_greyNicheOffers_niche1 === void 0 ? void 0 : _values_greyNicheOffers_niche1.linkInsertionPrice) || 0,\n                                                                                        onChange: (e)=>setFieldValue(\"greyNicheOffers.\".concat(niche, \".linkInsertionPrice\"), Number(e.target.value)),\n                                                                                        min: \"0\",\n                                                                                        max: \"10000\",\n                                                                                        placeholder: \"0\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                                        lineNumber: 233,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                                lineNumber: 227,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            ((_errors_greyNicheOffers1 = errors.greyNicheOffers) === null || _errors_greyNicheOffers1 === void 0 ? void 0 : (_errors_greyNicheOffers_niche2 = _errors_greyNicheOffers1[niche]) === null || _errors_greyNicheOffers_niche2 === void 0 ? void 0 : _errors_greyNicheOffers_niche2.linkInsertionPrice) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                className: \"text-xs text-errorbase\",\n                                                                                children: (_errors_greyNicheOffers_niche3 = errors.greyNicheOffers[niche]) === null || _errors_greyNicheOffers_niche3 === void 0 ? void 0 : _errors_greyNicheOffers_niche3.linkInsertionPrice\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                                lineNumber: 255,\n                                                                                columnNumber: 31\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                        lineNumber: 223,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                        lineNumber: 171,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, niche, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                    lineNumber: 170,\n                                                    columnNumber: 21\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                value: \"homepage\",\n                                className: \"mt-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-foreground-60 text-sm mb-6\",\n                                            children: \"Configure pricing and details for homepage link placement.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                            lineNumber: 274,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                            children: \"Homepage Link Price *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex w-full border border-solid border-[#eaeaea] rounded-md\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex w-10 h-10 items-center justify-center bg-secondary-bg100 rounded-[6px_0px_0px_6px] border border-solid border-[#eaeaea]\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-text-md-normal text-[length:var(--text-md-normal-font-size)] tracking-[var(--text-md-normal-letter-spacing)] leading-[var(--text-md-normal-line-height)] text-mutedbase text-center\",\n                                                                        children: \"$\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                        lineNumber: 286,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                    lineNumber: 285,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                                    className: \"w-full h-10 bg-secondary-bg100 rounded-[0px_6px_6px_0px] border-t border-r border-b border-[#eaeaea]\",\n                                                                    type: \"number\",\n                                                                    value: values.homepageOffer.price,\n                                                                    onChange: (e)=>setFieldValue(\"homepageOffer.price\", Number(e.target.value)),\n                                                                    min: \"0\",\n                                                                    max: \"50000\",\n                                                                    placeholder: \"Enter price\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                    lineNumber: 290,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                            lineNumber: 284,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        ((_errors_homepageOffer = errors.homepageOffer) === null || _errors_homepageOffer === void 0 ? void 0 : _errors_homepageOffer.price) && ((_touched_homepageOffer = touched.homepageOffer) === null || _touched_homepageOffer === void 0 ? void 0 : _touched_homepageOffer.price) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm text-errorbase\",\n                                                            children: errors.homepageOffer.price\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase\",\n                                                            children: \"Placement Description *\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                            lineNumber: 315,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_textarea__WEBPACK_IMPORTED_MODULE_4__.Textarea, {\n                                                            className: \"w-full min-h-[100px] bg-secondary-bg100 border border-[#eaeaea] rounded-md resize-none\",\n                                                            value: values.homepageOffer.description,\n                                                            onChange: (e)=>setFieldValue(\"homepageOffer.description\", e.target.value),\n                                                            placeholder: \"Describe the homepage link placement (e.g., sidebar, footer, header navigation, etc.)\",\n                                                            maxLength: 500\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                            lineNumber: 318,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between items-center\",\n                                                            children: [\n                                                                ((_errors_homepageOffer1 = errors.homepageOffer) === null || _errors_homepageOffer1 === void 0 ? void 0 : _errors_homepageOffer1.description) && ((_touched_homepageOffer1 = touched.homepageOffer) === null || _touched_homepageOffer1 === void 0 ? void 0 : _touched_homepageOffer1.description) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-errorbase\",\n                                                                    children: errors.homepageOffer.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                    lineNumber: 333,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs text-foreground-60 ml-auto\",\n                                                                    children: [\n                                                                        ((_values_homepageOffer_description = values.homepageOffer.description) === null || _values_homepageOffer_description === void 0 ? void 0 : _values_homepageOffer_description.length) || 0,\n                                                                        \"/500\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                                    lineNumber: 337,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                            lineNumber: 330,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                    lineNumber: 314,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                            lineNumber: 278,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-blue-900 mb-2\",\n                                                    children: \"Homepage Link Benefits\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                    className: \"text-sm text-blue-800 space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Maximum visibility and traffic potential\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                            lineNumber: 350,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Permanent placement (unless specified otherwise)\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Higher domain authority link value\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                            children: \"• Direct access from all website pages\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                                lineNumber: 272,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n                lineNumber: 35,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/components/CreateOfferSection.tsx\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, this);\n}\n_c = CreateOfferSection;\nvar _c;\n$RefreshReg$(_c, \"CreateOfferSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CreateOfferSection.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/add-website/page",{

/***/ "(app-pages-browser)/./src/app/add-website/page.tsx":
/*!**************************************!*\
  !*** ./src/app/add-website/page.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_AddWebsiteForm__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/AddWebsiteForm */ \"(app-pages-browser)/./src/components/AddWebsiteForm.tsx\");\n/* harmony import */ var _store_formStore__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/formStore */ \"(app-pages-browser)/./src/store/formStore.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst AddWebsitePage = ()=>{\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams)();\n    const { loadWebsiteForEdit, resetForm, editingWebsiteId } = (0,_store_formStore__WEBPACK_IMPORTED_MODULE_2__.useFormStore)();\n    const editId = searchParams.get(\"edit\");\n    const isEditMode = Boolean(editId);\n    (0,react__WEBPACK_IMPORTED_MODULE_4__.useEffect)(()=>{\n        if (editId) {\n            // Always ensure we have the correct data loaded for the edit ID\n            // This handles both initial navigation and page refresh scenarios\n            if (editId !== editingWebsiteId) {\n                loadWebsiteForEdit(editId);\n            }\n        } else if (!editId && editingWebsiteId) {\n            // Reset form when switching from edit to create mode\n            resetForm();\n        }\n    }, [\n        editId,\n        editingWebsiteId,\n        loadWebsiteForEdit,\n        resetForm\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        className: \"min-h-screen bg-background-25\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AddWebsiteForm__WEBPACK_IMPORTED_MODULE_1__.AddWebsiteForm, {\n            isEditMode: isEditMode\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/app/add-website/page.tsx\",\n            lineNumber: 30,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Gaurav/Projects/beyond-labs-task/src/app/add-website/page.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, undefined);\n};\n_s(AddWebsitePage, \"twrIeahxNYksgLpfFLU9JiyxO6A=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useSearchParams,\n        _store_formStore__WEBPACK_IMPORTED_MODULE_2__.useFormStore\n    ];\n});\n_c = AddWebsitePage;\n/* harmony default export */ __webpack_exports__[\"default\"] = (AddWebsitePage);\nvar _c;\n$RefreshReg$(_c, \"AddWebsitePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/add-website/page.tsx\n"));

/***/ })

});
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.VariableDefinition = void 0;
const DefinitionBase_1 = require("./DefinitionBase");
const DefinitionType_1 = require("./DefinitionType");
class VariableDefinition extends DefinitionBase_1.DefinitionBase {
    isTypeDefinition = false;
    isVariableDefinition = true;
    constructor(name, node, decl) {
        super(DefinitionType_1.DefinitionType.Variable, name, node, decl);
    }
}
exports.VariableDefinition = VariableDefinition;

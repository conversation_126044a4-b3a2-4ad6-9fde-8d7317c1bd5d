export interface Category {
  id: string;
  label: string;
  checked: boolean;
}

// Grey Niche Categories
export interface GreyNicheOffer {
  guestPostPrice: number;
  linkInsertionPrice: number;
}

export interface GreyNicheOffers {
  casino: GreyNicheOffer;
  cbd: GreyNicheOffer;
  crypto: GreyNicheOffer;
  forex: GreyNicheOffer;
  adult: GreyNicheOffer;
  vaping: GreyNicheOffer;
}

// Homepage Offer
export interface HomepageOffer {
  price: number;
  description: string;
}

// Complete Website Data Structure
export interface WebsiteFormData {
  // Basic Website Details
  websiteUrl: string;
  primaryLanguage: string;
  trafficCountry: string;
  categories: string[];
  description: string;
  isOwner: boolean;

  // Normal Offers
  guestPostingPrice: number;
  linkInsertionPrice: number;

  // Grey Niche Offers
  greyNicheOffers: GreyNicheOffers;

  // Homepage Offer
  homepageOffer: HomepageOffer;

  // Article Specifications
  isWritingIncluded: "yes" | "no";
  wordCountType: "unlimited" | "limited";
  minWords: number;
  maxWords: number;
  allowDofollow: "yes" | "no";
  linkType: "brand" | "branded-generic" | "mixed" | "all";
  taggingPolicy: "no-tag" | "tag-request" | "always-tag" | "all-links-tag";
  linkNumberType: "unlimited" | "limited";
  minLinks: number;
  maxLinks: number;
  otherLinksPolicy: "allow" | "no-allow";
  contentRules: string;

  // Additional Article Specification Fields
  acceptedContentTypes: string[];
  turnaroundTime: number; // in days
  revisionPolicy: "no-revisions" | "one-revision" | "unlimited-revisions";
  contentGuidelines: string;
  prohibitedTopics: string[];
  requiredDisclosures: boolean;
  socialMediaPromotion: boolean;
  metaDataRequirements: string;
}

// Website Data for Display (includes ID and timestamps)
export interface Website extends WebsiteFormData {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  status: "active" | "inactive" | "pending";
}

export interface FormErrors {
  [key: string]: string | undefined;
}

export interface OfferType {
  id: string;
  label: string;
  active: boolean;
}

// Pagination and Table State
export interface PaginationState {
  currentPage: number;
  pageSize: number;
  totalItems: number;
  totalPages: number;
}

export interface TableFilters {
  search: string;
  language: string;
  country: string;
  category: string;
}

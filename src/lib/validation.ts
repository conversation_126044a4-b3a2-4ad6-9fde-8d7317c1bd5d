import { z } from "zod";

// Grey Niche Offer Schema
const greyNicheOfferSchema = z.object({
  guestPostPrice: z
    .number()
    .min(0, "Price cannot be negative")
    .max(10000, "Price cannot exceed $10,000"),
  linkInsertionPrice: z
    .number()
    .min(0, "Price cannot be negative")
    .max(10000, "Price cannot exceed $10,000"),
});

// Homepage Offer Schema
const homepageOfferSchema = z.object({
  price: z
    .number()
    .min(0, "Price cannot be negative")
    .max(50000, "Price cannot exceed $50,000"),
  description: z
    .string()
    .max(500, "Description must be less than 500 characters"),
});

export const websiteFormSchema = z
  .object({
    // Basic Website Details
    websiteUrl: z
      .string()
      .min(1, "Website URL is required")
      .url("Please enter a valid URL"),

    primaryLanguage: z.string().min(1, "Primary language is required"),

    trafficCountry: z.string().min(1, "Traffic country is required"),

    categories: z
      .array(z.string())
      .min(1, "Please select at least one category"),

    description: z
      .string()
      .min(10, "Description must be at least 10 characters")
      .max(500, "Description must be less than 500 characters"),

    isOwner: z
      .boolean()
      .refine(
        (val) => val === true,
        "You must confirm you are the website owner"
      ),

    // Normal Offers
    guestPostingPrice: z
      .number()
      .min(1, "Guest posting price must be at least $1")
      .max(10000, "Price cannot exceed $10,000"),

    linkInsertionPrice: z
      .number()
      .min(1, "Link insertion price must be at least $1")
      .max(10000, "Price cannot exceed $10,000"),

    // Grey Niche Offers
    greyNicheOffers: z.object({
      casino: greyNicheOfferSchema,
      cbd: greyNicheOfferSchema,
      crypto: greyNicheOfferSchema,
      forex: greyNicheOfferSchema,
      adult: greyNicheOfferSchema,
      vaping: greyNicheOfferSchema,
    }),

    // Homepage Offer
    homepageOffer: homepageOfferSchema,

    // Article Specifications
    isWritingIncluded: z.enum(["yes", "no"]),

    wordCountType: z.enum(["unlimited", "limited"]),

    minWords: z.number().min(0, "Minimum words cannot be negative").optional(),

    maxWords: z.number().min(0, "Maximum words cannot be negative").optional(),

    allowDofollow: z.enum(["yes", "no"]),

    linkType: z.enum(["brand", "branded-generic", "mixed", "all"]),

    taggingPolicy: z.enum([
      "no-tag",
      "tag-request",
      "always-tag",
      "all-links-tag",
    ]),

    linkNumberType: z.enum(["unlimited", "limited"]),

    minLinks: z.number().min(0, "Minimum links cannot be negative").optional(),

    maxLinks: z.number().min(0, "Maximum links cannot be negative").optional(),

    otherLinksPolicy: z.enum(["allow", "no-allow"]),

    contentRules: z
      .string()
      .max(1000, "Content rules must be less than 1000 characters")
      .optional(),

    // Additional Article Specification Fields
    acceptedContentTypes: z
      .array(z.string())
      .min(1, "Please select at least one content type"),

    turnaroundTime: z
      .number()
      .min(1, "Turnaround time must be at least 1 day")
      .max(365, "Turnaround time cannot exceed 365 days"),

    revisionPolicy: z.enum([
      "no-revisions",
      "one-revision",
      "unlimited-revisions",
    ]),

    contentGuidelines: z
      .string()
      .max(1000, "Content guidelines must be less than 1000 characters")
      .optional(),

    prohibitedTopics: z.array(z.string()),

    requiredDisclosures: z.boolean(),

    socialMediaPromotion: z.boolean(),

    metaDataRequirements: z
      .string()
      .max(500, "Meta data requirements must be less than 500 characters")
      .optional(),
  })
  .refine(
    (data) => {
      if (data.wordCountType === "limited") {
        return (
          data.minWords !== undefined &&
          data.maxWords !== undefined &&
          data.minWords <= data.maxWords
        );
      }
      return true;
    },
    {
      message: "Maximum words must be greater than or equal to minimum words",
      path: ["maxWords"],
    }
  )
  .refine(
    (data) => {
      if (data.linkNumberType === "limited") {
        return (
          data.minLinks !== undefined &&
          data.maxLinks !== undefined &&
          data.minLinks <= data.maxLinks
        );
      }
      return true;
    },
    {
      message: "Maximum links must be greater than or equal to minimum links",
      path: ["maxLinks"],
    }
  );

export type WebsiteFormSchema = z.infer<typeof websiteFormSchema>;

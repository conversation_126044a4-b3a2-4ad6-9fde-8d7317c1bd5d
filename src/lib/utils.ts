import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// Learning points for InfoCard
export const learningPoints = [
  "How to optimize your website for better performance",
  "Best practices for SEO and content marketing",
  "Understanding analytics and user behavior",
  "Monetization strategies that actually work",
  "Building a sustainable online presence",
];

// Language options for website form
export const languageOptions = [
  { value: "english", label: "English", flag: "🇺🇸" },
  { value: "spanish", label: "Spanish", flag: "🇪🇸" },
  { value: "french", label: "French", flag: "🇫🇷" },
  { value: "german", label: "German", flag: "🇩🇪" },
  { value: "italian", label: "Italian", flag: "🇮🇹" },
  { value: "portuguese", label: "Portuguese", flag: "🇵🇹" },
];

// Country options for traffic source
export const countryOptions = [
  { value: "us", label: "United States", flag: "🇺🇸" },
  { value: "uk", label: "United Kingdom", flag: "🇬🇧" },
  { value: "ca", label: "Canada", flag: "🇨🇦" },
  { value: "au", label: "Australia", flag: "🇦🇺" },
  { value: "de", label: "Germany", flag: "🇩🇪" },
  { value: "fr", label: "France", flag: "🇫🇷" },
  { value: "es", label: "Spain", flag: "🇪🇸" },
  { value: "it", label: "Italy", flag: "🇮🇹" },
];

// Categories for website classification
export const categories = [
  [
    { id: "technology", label: "Technology" },
    { id: "business", label: "Business" },
    { id: "health", label: "Health & Fitness" },
    { id: "lifestyle", label: "Lifestyle" },
    { id: "education", label: "Education" },
  ],
  [
    { id: "entertainment", label: "Entertainment" },
    { id: "news", label: "News & Media" },
    { id: "sports", label: "Sports" },
    { id: "travel", label: "Travel" },
    { id: "food", label: "Food & Cooking" },
  ],
  [
    { id: "fashion", label: "Fashion & Beauty" },
    { id: "finance", label: "Finance" },
    { id: "automotive", label: "Automotive" },
    { id: "real-estate", label: "Real Estate" },
    { id: "gaming", label: "Gaming" },
  ],
  [
    { id: "parenting", label: "Parenting" },
    { id: "pets", label: "Pets & Animals" },
    { id: "home-garden", label: "Home & Garden" },
    { id: "art-design", label: "Art & Design" },
    { id: "music", label: "Music" },
  ],
  [
    { id: "photography", label: "Photography" },
    { id: "science", label: "Science" },
    { id: "politics", label: "Politics" },
    { id: "religion", label: "Religion" },
    { id: "other", label: "Other" },
  ],
];

import React from "react";
import { FormikErrors, FormikTouched } from "formik";
import { WebsiteFormSchema } from "@/lib/validation";
import { Card, CardContent } from "./ui/card";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "./ui/tabs";
import {
  FaDice,
  FaLeaf,
  FaBitcoin,
  FaDollarSign,
  FaHeart,
  FaSmoking,
} from "react-icons/fa";

interface CreateOfferSectionProps {
  values: WebsiteFormSchema;
  setFieldValue: (field: string, value: any) => void;
  errors: FormikErrors<WebsiteFormSchema>;
  touched: FormikTouched<WebsiteFormSchema>;
}

export function CreateOfferSection({
  values,
  setFieldValue,
  errors,
  touched,
}: CreateOfferSectionProps) {
  return (
    <div className="flex flex-col items-start gap-5 w-full">
      <h2 className="font-heading-h3 text-[length:var(--heading-h3-font-size)] tracking-[var(--heading-h3-letter-spacing)] leading-[var(--heading-h3-line-height)] text-foregroundbase">
        Create offer
      </h2>
      <Card className="p-6 bg-[#ffffff] rounded-lg shadow-shadow-sm w-full">
        <CardContent className="p-0 flex flex-col gap-12">
          <Tabs defaultValue="normal" className="w-full">
            <TabsList className="flex gap-12 border-b border-[#eaeaea] w-full justify-start h-12 bg-transparent p-0">
              <TabsTrigger
                value="normal"
                className="font-heading-h5 text-[length:var(--heading-h5-font-size)] tracking-[var(--heading-h5-letter-spacing)] leading-[var(--heading-h5-line-height)] data-[state=active]:text-foregroundbase data-[state=active]:border-b-2 data-[state=active]:border-[#613fdd] data-[state=inactive]:text-foreground-40 rounded-none px-2.5 py-2.5"
              >
                Normal offer
              </TabsTrigger>
              <TabsTrigger
                value="grey"
                className="font-heading-h5 text-[length:var(--heading-h5-font-size)] tracking-[var(--heading-h5-letter-spacing)] leading-[var(--heading-h5-line-height)] data-[state=active]:text-foregroundbase data-[state=active]:border-b-2 data-[state=active]:border-[#613fdd] data-[state=inactive]:text-foreground-40 rounded-none px-2.5 py-2.5"
              >
                Grey Niche offer
              </TabsTrigger>
              <TabsTrigger
                value="homepage"
                className="font-heading-h5 text-[length:var(--heading-h5-font-size)] tracking-[var(--heading-h5-letter-spacing)] leading-[var(--heading-h5-line-height)] data-[state=active]:text-foregroundbase data-[state=active]:border-b-2 data-[state=active]:border-[#613fdd] data-[state=inactive]:text-foreground-40 rounded-none px-2.5 py-2.5"
              >
                Homepage link
              </TabsTrigger>
            </TabsList>

            <TabsContent value="normal" className="mt-10">
              <div className="flex flex-col md:flex-row items-start gap-8 w-full">
                <div className="flex flex-col items-start gap-5">
                  <div className="flex items-center gap-8">
                    <div className="flex flex-col w-full md:w-[262px] items-start gap-2">
                      <label className="font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase">
                        Guest posting *
                      </label>
                      <div className="flex w-full border border-solid border-[#eaeaea] rounded-md">
                        <div className="flex w-10 h-10 items-center justify-center bg-secondary-bg100 rounded-[6px_0px_0px_6px] border border-solid border-[#eaeaea]">
                          <span className="font-text-md-normal text-[length:var(--text-md-normal-font-size)] tracking-[var(--text-md-normal-letter-spacing)] leading-[var(--text-md-normal-line-height)] text-mutedbase text-center">
                            $
                          </span>
                        </div>
                        <Input
                          className="w-full md:w-[222px] h-10 bg-secondary-bg100 rounded-[0px_6px_6px_0px] border-t border-r border-b border-[#eaeaea]"
                          type="number"
                          value={values.guestPostingPrice}
                          onChange={(e) =>
                            setFieldValue(
                              "guestPostingPrice",
                              Number(e.target.value)
                            )
                          }
                          min="1"
                          max="10000"
                        />
                      </div>
                      {errors.guestPostingPrice &&
                        touched.guestPostingPrice && (
                          <span className="text-sm text-errorbase">
                            {errors.guestPostingPrice}
                          </span>
                        )}
                    </div>
                  </div>
                </div>

                <div className="flex flex-col items-start gap-5">
                  <div className="flex items-center gap-8">
                    <div className="flex flex-col w-full md:w-[262px] items-start gap-2">
                      <label className="font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase">
                        Link insertion *
                      </label>
                      <div className="flex w-full border border-solid border-[#eaeaea] rounded-md">
                        <div className="flex w-10 h-10 items-center justify-center bg-secondary-bg100 rounded-[6px_0px_0px_6px] border border-solid border-[#eaeaea]">
                          <span className="font-text-md-normal text-[length:var(--text-md-normal-font-size)] tracking-[var(--text-md-normal-letter-spacing)] leading-[var(--text-md-normal-line-height)] text-mutedbase text-center">
                            $
                          </span>
                        </div>
                        <Input
                          className="w-full md:w-[222px] h-10 bg-secondary-bg100 rounded-[0px_6px_6px_0px] border-t border-r border-b border-[#eaeaea]"
                          type="number"
                          value={values.linkInsertionPrice}
                          onChange={(e) =>
                            setFieldValue(
                              "linkInsertionPrice",
                              Number(e.target.value)
                            )
                          }
                          min="1"
                          max="10000"
                        />
                      </div>
                      {errors.linkInsertionPrice &&
                        touched.linkInsertionPrice && (
                          <span className="text-sm text-errorbase">
                            {errors.linkInsertionPrice}
                          </span>
                        )}
                    </div>
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="grey" className="mt-10">
              <div className="space-y-8">
                <p className="text-foreground-60 text-sm mb-6">
                  Set pricing for grey niche categories. Leave at $0 if you
                  don't accept that niche.
                </p>

                {/* Grey Niche Categories Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {Object.entries({
                    casino: {
                      label: "Casino & Gambling",
                      color: "text-red-600",
                    },
                    cbd: {
                      label: "CBD & Cannabis",
                      color: "text-green-600",
                    },
                    crypto: {
                      label: "Cryptocurrency",
                      color: "text-orange-600",
                    },
                    forex: {
                      label: "Forex Trading",
                      color: "text-blue-600",
                    },
                    adult: {
                      label: "Adult Content",
                      color: "text-pink-600",
                    },
                    vaping: {
                      label: "Vaping & E-cigarettes",
                      color: "text-purple-600",
                    },
                  }).map(([niche, config]) => (
                    <Card key={niche} className="p-4 border border-gray-200">
                      <CardContent className="p-0 space-y-4">
                        <div className="flex items-center gap-3">
                          <h4 className="font-medium text-foregroundbase">
                            {config.label}
                          </h4>
                        </div>

                        <div className="space-y-3">
                          {/* Guest Post Price */}
                          <div className="flex flex-col gap-2">
                            <label className="text-sm font-medium text-foregroundbase">
                              Guest Post Price
                            </label>
                            <div className="flex border border-solid border-[#eaeaea] rounded-md">
                              <div className="flex w-8 h-9 items-center justify-center bg-secondary-bg100 rounded-[6px_0px_0px_6px] border-r border-[#eaeaea]">
                                <span className="text-sm text-mutedbase">
                                  $
                                </span>
                              </div>
                              <Input
                                className="h-9 bg-secondary-bg100 rounded-[0px_6px_6px_0px] border-0"
                                type="number"
                                value={
                                  values.greyNicheOffers[
                                    niche as keyof typeof values.greyNicheOffers
                                  ]?.guestPostPrice || 0
                                }
                                onChange={(e) =>
                                  setFieldValue(
                                    `greyNicheOffers.${niche}.guestPostPrice`,
                                    Number(e.target.value)
                                  )
                                }
                                min="0"
                                max="10000"
                                placeholder="0"
                              />
                            </div>
                            {errors.greyNicheOffers?.[
                              niche as keyof typeof errors.greyNicheOffers
                            ]?.guestPostPrice && (
                              <span className="text-xs text-errorbase">
                                {
                                  errors.greyNicheOffers[
                                    niche as keyof typeof errors.greyNicheOffers
                                  ]?.guestPostPrice
                                }
                              </span>
                            )}
                          </div>

                          {/* Link Insertion Price */}
                          <div className="flex flex-col gap-2">
                            <label className="text-sm font-medium text-foregroundbase">
                              Link Insertion Price
                            </label>
                            <div className="flex border border-solid border-[#eaeaea] rounded-md">
                              <div className="flex w-8 h-9 items-center justify-center bg-secondary-bg100 rounded-[6px_0px_0px_6px] border-r border-[#eaeaea]">
                                <span className="text-sm text-mutedbase">
                                  $
                                </span>
                              </div>
                              <Input
                                className="h-9 bg-secondary-bg100 rounded-[0px_6px_6px_0px] border-0"
                                type="number"
                                value={
                                  values.greyNicheOffers[
                                    niche as keyof typeof values.greyNicheOffers
                                  ]?.linkInsertionPrice || 0
                                }
                                onChange={(e) =>
                                  setFieldValue(
                                    `greyNicheOffers.${niche}.linkInsertionPrice`,
                                    Number(e.target.value)
                                  )
                                }
                                min="0"
                                max="10000"
                                placeholder="0"
                              />
                            </div>
                            {errors.greyNicheOffers?.[
                              niche as keyof typeof errors.greyNicheOffers
                            ]?.linkInsertionPrice && (
                              <span className="text-xs text-errorbase">
                                {
                                  errors.greyNicheOffers[
                                    niche as keyof typeof errors.greyNicheOffers
                                  ]?.linkInsertionPrice
                                }
                              </span>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="homepage" className="mt-10">
              <div className="space-y-6">
                <p className="text-foreground-60 text-sm mb-6">
                  Configure pricing and details for homepage link placement.
                </p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* Homepage Price */}
                  <div className="flex flex-col gap-2">
                    <label className="font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase">
                      Homepage Link Price *
                    </label>
                    <div className="flex w-full border border-solid border-[#eaeaea] rounded-md">
                      <div className="flex w-10 h-10 items-center justify-center bg-secondary-bg100 rounded-[6px_0px_0px_6px] border border-solid border-[#eaeaea]">
                        <span className="font-text-md-normal text-[length:var(--text-md-normal-font-size)] tracking-[var(--text-md-normal-letter-spacing)] leading-[var(--text-md-normal-line-height)] text-mutedbase text-center">
                          $
                        </span>
                      </div>
                      <Input
                        className="w-full h-10 bg-secondary-bg100 rounded-[0px_6px_6px_0px] border-t border-r border-b border-[#eaeaea]"
                        type="number"
                        value={values.homepageOffer.price}
                        onChange={(e) =>
                          setFieldValue(
                            "homepageOffer.price",
                            Number(e.target.value)
                          )
                        }
                        min="0"
                        max="50000"
                        placeholder="Enter price"
                      />
                    </div>
                    {errors.homepageOffer?.price &&
                      touched.homepageOffer?.price && (
                        <span className="text-sm text-errorbase">
                          {errors.homepageOffer.price}
                        </span>
                      )}
                  </div>

                  {/* Homepage Description */}
                  <div className="flex flex-col gap-2">
                    <label className="font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase">
                      Placement Description *
                    </label>
                    <Textarea
                      className="w-full min-h-[100px] bg-secondary-bg100 border border-[#eaeaea] rounded-md resize-none"
                      value={values.homepageOffer.description}
                      onChange={(e) =>
                        setFieldValue(
                          "homepageOffer.description",
                          e.target.value
                        )
                      }
                      placeholder="Describe the homepage link placement (e.g., sidebar, footer, header navigation, etc.)"
                      maxLength={500}
                    />
                    <div className="flex justify-between items-center">
                      {errors.homepageOffer?.description &&
                        touched.homepageOffer?.description && (
                          <span className="text-sm text-errorbase">
                            {errors.homepageOffer.description}
                          </span>
                        )}
                      <span className="text-xs text-foreground-60 ml-auto">
                        {values.homepageOffer.description?.length || 0}/500
                      </span>
                    </div>
                  </div>
                </div>

                {/* Additional Homepage Offer Information */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">
                    Homepage Link Benefits
                  </h4>
                  <ul className="text-sm text-blue-800 space-y-1">
                    <li>• Maximum visibility and traffic potential</li>
                    <li>• Permanent placement (unless specified otherwise)</li>
                    <li>• Higher domain authority link value</li>
                    <li>• Direct access from all website pages</li>
                  </ul>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

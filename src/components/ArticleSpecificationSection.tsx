import React, { useState } from "react";
import { FormikErrors, FormikTouched } from "formik";
import { WebsiteFormSchema } from "@/lib/validation";
import { Card, CardContent } from "./ui/card";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";
import { RadioGroup, RadioGroupItem } from "./ui/radio-group";
import { Checkbox } from "./ui/checkbox";
import { Button } from "./ui/button";
import { Badge } from "./ui/badge";
import { Separator } from "./ui/separator";
import { Plus, X } from "lucide-react";

interface ArticleSpecificationSectionProps {
  values: WebsiteFormSchema;
  setFieldValue: (field: string, value: any) => void;
  errors: FormikErrors<WebsiteFormSchema>;
  touched: FormikTouched<WebsiteFormSchema>;
}

export function ArticleSpecificationSection({
  values,
  setFieldValue,
  errors,
  touched,
}: ArticleSpecificationSectionProps) {
  const articleWritingOptions = [
    { id: "yes", label: "Yes" },
    {
      id: "no",
      label: "No, the advertiser (client) needs to provide the content",
    },
  ];

  const wordCountOptions = [
    { id: "unlimited", label: "Length of the article is not limited." },
    { id: "limited", label: "Set minimum and maximum word count" },
  ];

  const dofollowOptions = [
    { id: "yes", label: "Yes" },
    { id: "no", label: "No" },
  ];

  const linkTypeOptions = [
    {
      id: "brand",
      label: "Only brand links, URL, navigational, graphic links.",
    },
    { id: "branded-generic", label: "Only branded and generic links." },
    { id: "mixed", label: "Also mixed links (partly exact match anchors)." },
    { id: "all", label: "All links, including exact match anchors." },
  ];

  const taggingOptions = [
    { id: "no-tag", label: "We do not tag paid articles." },
    {
      id: "tag-request",
      label: "Articles are tagged only at the advertiser's request.",
    },
    { id: "always-tag", label: 'We always tag articles: "Sponsored article".' },
    { id: "all-links-tag", label: "All links, including exact match anchors." },
  ];

  const linkNumberOptions = [
    { id: "unlimited", label: "Number of links is not limited." },
    { id: "limited", label: "A maximum number of links to the advertiser:" },
  ];

  const otherLinksOptions = [
    {
      id: "allow",
      label: "We allow links to other websites in the content of the article.",
    },
    {
      id: "no-allow",
      label:
        "We DO NOT allow links to other websites in the content of the article.",
    },
  ];

  return (
    <div className="flex flex-col w-full items-start gap-5">
      <h2 className="font-heading-h3 text-[length:var(--heading-h3-font-size)] tracking-[var(--heading-h3-letter-spacing)] leading-[var(--heading-h3-line-height)] text-foregroundbase">
        Article specification
      </h2>
      <Card className="w-full p-6 bg-uicard rounded-lg">
        <CardContent className="p-0">
          <div className="flex flex-col lg:flex-row items-start gap-20 w-full">
            {/* Left Column */}
            <div className="flex flex-col w-full lg:w-[400px] items-start gap-10">
              {/* Writing Included */}
              <div className="flex flex-col items-start gap-6 w-full">
                <p className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foregroundbase">
                  Is writing of an article included in the offer?
                </p>
                <RadioGroup
                  value={values.isWritingIncluded}
                  onValueChange={(value) =>
                    setFieldValue("isWritingIncluded", value)
                  }
                  className="flex flex-col gap-3 w-full"
                >
                  {articleWritingOptions.map((option) => (
                    <div
                      key={option.id}
                      className="flex items-center gap-2 w-full"
                    >
                      <RadioGroupItem
                        value={option.id}
                        id={`writing-${option.id}`}
                        className="w-4 h-4"
                      />
                      <label
                        htmlFor={`writing-${option.id}`}
                        className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foreground-60 cursor-pointer"
                      >
                        {option.label}
                      </label>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              {/* Word Count */}
              <div className="flex flex-col items-start gap-2.5 w-full">
                <div className="flex flex-col items-start gap-6 w-full">
                  <p className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foregroundbase">
                    Number of words in the article
                  </p>
                  <RadioGroup
                    value={values.wordCountType}
                    onValueChange={(value) =>
                      setFieldValue("wordCountType", value)
                    }
                    className="flex flex-col gap-3 w-full"
                  >
                    {wordCountOptions.map((option) => (
                      <div
                        key={option.id}
                        className="flex items-center gap-2 w-full"
                      >
                        <RadioGroupItem
                          value={option.id}
                          id={`wordcount-${option.id}`}
                          className="w-4 h-4"
                        />
                        <label
                          htmlFor={`wordcount-${option.id}`}
                          className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foreground-60 cursor-pointer"
                        >
                          {option.label}
                        </label>
                      </div>
                    ))}
                  </RadioGroup>
                </div>

                {values.wordCountType === "limited" && (
                  <div className="pl-6">
                    <div className="flex w-full max-w-[237px] h-10 items-center justify-between gap-2">
                      <Input
                        className="w-[95px] h-10 bg-uicard border border-solid border-[#eaeaea]"
                        placeholder="Min"
                        type="number"
                        value={values.minWords || ""}
                        onChange={(e) =>
                          setFieldValue("minWords", Number(e.target.value))
                        }
                        min="0"
                      />
                      <Separator className="w-2 h-px bg-mildbase rounded-[1px]" />
                      <Input
                        className="w-[95px] h-10 bg-uicard border border-solid border-[#eaeaea]"
                        placeholder="Max"
                        type="number"
                        value={values.maxWords || ""}
                        onChange={(e) =>
                          setFieldValue("maxWords", Number(e.target.value))
                        }
                        min="0"
                      />
                    </div>
                    {errors.maxWords && touched.maxWords && (
                      <span className="text-sm text-errorbase mt-1">
                        {errors.maxWords}
                      </span>
                    )}
                  </div>
                )}
              </div>

              {/* Dofollow Links */}
              <div className="flex flex-col items-start gap-6 w-full">
                <p className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foregroundbase">
                  I allow DOFOLLOW links in the article
                </p>
                <RadioGroup
                  value={values.allowDofollow}
                  onValueChange={(value) =>
                    setFieldValue("allowDofollow", value)
                  }
                  className="flex flex-col gap-3 w-full"
                >
                  {dofollowOptions.map((option) => (
                    <div
                      key={option.id}
                      className="flex items-center gap-2 w-full"
                    >
                      <RadioGroupItem
                        value={option.id}
                        id={`dofollow-${option.id}`}
                        className="w-4 h-4"
                      />
                      <label
                        htmlFor={`dofollow-${option.id}`}
                        className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foreground-60 cursor-pointer"
                      >
                        {option.label}
                      </label>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              {/* Link Types */}
              <div className="flex flex-col items-start gap-6 w-full">
                <p className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foregroundbase">
                  Type of links allowed:
                </p>
                <RadioGroup
                  value={values.linkType}
                  onValueChange={(value) => setFieldValue("linkType", value)}
                  className="flex flex-col gap-3 w-full"
                >
                  {linkTypeOptions.map((option) => (
                    <div
                      key={option.id}
                      className="flex items-center gap-2 w-full"
                    >
                      <RadioGroupItem
                        value={option.id}
                        id={`linktype-${option.id}`}
                        className="w-4 h-4"
                      />
                      <label
                        htmlFor={`linktype-${option.id}`}
                        className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foreground-60 cursor-pointer"
                      >
                        {option.label}
                      </label>
                    </div>
                  ))}
                </RadioGroup>
              </div>
            </div>

            {/* Right Column */}
            <div className="flex flex-col w-full lg:w-[471px] items-start gap-10">
              {/* Tagging Policy */}
              <div className="flex flex-col w-full lg:w-[400px] items-start gap-6">
                <p className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foregroundbase">
                  Tagging articles policy:
                </p>
                <RadioGroup
                  value={values.taggingPolicy}
                  onValueChange={(value) =>
                    setFieldValue("taggingPolicy", value)
                  }
                  className="flex flex-col gap-3 w-full"
                >
                  {taggingOptions.map((option) => (
                    <div
                      key={option.id}
                      className="flex items-center gap-2 w-full"
                    >
                      <RadioGroupItem
                        value={option.id}
                        id={`tagging-${option.id}`}
                        className="w-4 h-4"
                      />
                      <label
                        htmlFor={`tagging-${option.id}`}
                        className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foreground-60 cursor-pointer"
                      >
                        {option.label}
                      </label>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              {/* Number of Links */}
              <div className="flex flex-col w-full lg:w-[400px] items-start gap-2.5">
                <div className="flex flex-col items-start gap-6 w-full">
                  <p className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foregroundbase">
                    A number of links to the advertiser in the article:
                  </p>
                  <RadioGroup
                    value={values.linkNumberType}
                    onValueChange={(value) =>
                      setFieldValue("linkNumberType", value)
                    }
                    className="flex flex-col gap-3 w-full"
                  >
                    {linkNumberOptions.map((option) => (
                      <div
                        key={option.id}
                        className="flex items-center gap-2 w-full"
                      >
                        <RadioGroupItem
                          value={option.id}
                          id={`linknumber-${option.id}`}
                          className="w-4 h-4"
                        />
                        <label
                          htmlFor={`linknumber-${option.id}`}
                          className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foreground-60 cursor-pointer"
                        >
                          {option.label}
                        </label>
                      </div>
                    ))}
                  </RadioGroup>
                </div>

                {values.linkNumberType === "limited" && (
                  <div className="pl-6">
                    <div className="flex w-full max-w-[237px] h-10 items-center justify-between gap-2">
                      <Input
                        className="w-[95px] h-10 bg-uicard border border-solid border-[#eaeaea]"
                        placeholder="Min"
                        type="number"
                        value={values.minLinks || ""}
                        onChange={(e) =>
                          setFieldValue("minLinks", Number(e.target.value))
                        }
                        min="0"
                      />
                      <Separator className="w-2 h-px bg-mildbase rounded-[1px]" />
                      <Input
                        className="w-[95px] h-10 bg-uicard border border-solid border-[#eaeaea]"
                        placeholder="Max"
                        type="number"
                        value={values.maxLinks || ""}
                        onChange={(e) =>
                          setFieldValue("maxLinks", Number(e.target.value))
                        }
                        min="0"
                      />
                    </div>
                    {errors.maxLinks && touched.maxLinks && (
                      <span className="text-sm text-errorbase mt-1">
                        {errors.maxLinks}
                      </span>
                    )}
                  </div>
                )}
              </div>

              {/* Other Links */}
              <div className="flex flex-col w-full lg:w-[400px] items-start gap-6">
                <p className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foregroundbase">
                  Other links in the article:
                </p>
                <RadioGroup
                  value={values.otherLinksPolicy}
                  onValueChange={(value) =>
                    setFieldValue("otherLinksPolicy", value)
                  }
                  className="flex flex-col gap-3 w-full"
                >
                  {otherLinksOptions.map((option) => (
                    <div
                      key={option.id}
                      className="flex items-center gap-2 w-full"
                    >
                      <RadioGroupItem
                        value={option.id}
                        id={`otherlinks-${option.id}`}
                        className="w-4 h-4"
                      />
                      <label
                        htmlFor={`otherlinks-${option.id}`}
                        className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foreground-60 cursor-pointer"
                      >
                        {option.label}
                      </label>
                    </div>
                  ))}
                </RadioGroup>
              </div>

              {/* Other Content Rules */}
              <div className="flex flex-col items-start gap-2 w-full">
                <label className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foregroundbase">
                  Other content rules/specifications:
                </label>
                <Textarea
                  className="h-[145px] bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm"
                  placeholder="Any additional rules or specifications for content..."
                  value={values.contentRules}
                  onChange={(e) =>
                    setFieldValue("contentRules", e.target.value)
                  }
                />
                {errors.contentRules && touched.contentRules && (
                  <span className="text-sm text-errorbase">
                    {errors.contentRules}
                  </span>
                )}
              </div>
            </div>
          </div>

          {/* Additional Article Specifications */}
          <div className="mt-12 pt-8 border-t border-[#eaeaea]">
            <h3 className="font-heading-h4 text-[length:var(--heading-h4-font-size)] tracking-[var(--heading-h4-letter-spacing)] leading-[var(--heading-h4-line-height)] text-foregroundbase mb-8">
              Additional Content Guidelines
            </h3>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Left Column - Additional Fields */}
              <div className="space-y-8">
                {/* Accepted Content Types */}
                <div className="flex flex-col gap-4">
                  <label className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foregroundbase">
                    Accepted Content Types *
                  </label>
                  <div className="space-y-3">
                    {[
                      "How-to guides",
                      "Industry insights",
                      "Case studies",
                      "Product reviews",
                      "News articles",
                      "Opinion pieces",
                      "Interviews",
                      "Listicles",
                      "Research reports",
                      "Tutorials",
                    ].map((contentType) => (
                      <div
                        key={contentType}
                        className="flex items-center gap-2"
                      >
                        <Checkbox
                          id={`content-${contentType}`}
                          checked={
                            values.acceptedContentTypes?.includes(
                              contentType
                            ) || false
                          }
                          onCheckedChange={(checked) => {
                            const currentTypes =
                              values.acceptedContentTypes || [];
                            if (checked) {
                              setFieldValue("acceptedContentTypes", [
                                ...currentTypes,
                                contentType,
                              ]);
                            } else {
                              setFieldValue(
                                "acceptedContentTypes",
                                currentTypes.filter(
                                  (type) => type !== contentType
                                )
                              );
                            }
                          }}
                        />
                        <label
                          htmlFor={`content-${contentType}`}
                          className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foreground-60 cursor-pointer"
                        >
                          {contentType}
                        </label>
                      </div>
                    ))}
                  </div>
                  {errors.acceptedContentTypes &&
                    touched.acceptedContentTypes && (
                      <span className="text-sm text-errorbase">
                        {errors.acceptedContentTypes}
                      </span>
                    )}
                </div>

                {/* Turnaround Time */}
                <div className="flex flex-col gap-2">
                  <label className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foregroundbase">
                    Turnaround Time (days) *
                  </label>
                  <Input
                    className="w-32 h-10 bg-uicard border border-solid border-[#eaeaea]"
                    type="number"
                    value={values.turnaroundTime}
                    onChange={(e) =>
                      setFieldValue("turnaroundTime", Number(e.target.value))
                    }
                    min="1"
                    max="365"
                    placeholder="7"
                  />
                  {errors.turnaroundTime && touched.turnaroundTime && (
                    <span className="text-sm text-errorbase">
                      {errors.turnaroundTime}
                    </span>
                  )}
                </div>

                {/* Revision Policy */}
                <div className="flex flex-col gap-4">
                  <label className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foregroundbase">
                    Revision Policy *
                  </label>
                  <RadioGroup
                    value={values.revisionPolicy}
                    onValueChange={(value) =>
                      setFieldValue("revisionPolicy", value)
                    }
                    className="flex flex-col gap-3"
                  >
                    {[
                      { id: "no-revisions", label: "No revisions allowed" },
                      { id: "one-revision", label: "One revision included" },
                      {
                        id: "unlimited-revisions",
                        label: "Unlimited revisions",
                      },
                    ].map((option) => (
                      <div key={option.id} className="flex items-center gap-2">
                        <RadioGroupItem
                          value={option.id}
                          id={`revision-${option.id}`}
                          className="w-4 h-4"
                        />
                        <label
                          htmlFor={`revision-${option.id}`}
                          className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foreground-60 cursor-pointer"
                        >
                          {option.label}
                        </label>
                      </div>
                    ))}
                  </RadioGroup>
                  {errors.revisionPolicy && touched.revisionPolicy && (
                    <span className="text-sm text-errorbase">
                      {errors.revisionPolicy}
                    </span>
                  )}
                </div>
              </div>

              {/* Right Column - Additional Fields */}
              <div className="space-y-8">
                {/* Content Guidelines */}
                <div className="flex flex-col gap-2">
                  <label className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foregroundbase">
                    Content Guidelines
                  </label>
                  <Textarea
                    className="h-[120px] bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm resize-none"
                    placeholder="Specific guidelines for content creation, tone, style, etc..."
                    value={values.contentGuidelines}
                    onChange={(e) =>
                      setFieldValue("contentGuidelines", e.target.value)
                    }
                    maxLength={1000}
                  />
                  <div className="flex justify-between items-center">
                    {errors.contentGuidelines && touched.contentGuidelines && (
                      <span className="text-sm text-errorbase">
                        {errors.contentGuidelines}
                      </span>
                    )}
                    <span className="text-xs text-foreground-60 ml-auto">
                      {values.contentGuidelines?.length || 0}/1000
                    </span>
                  </div>
                </div>

                {/* Prohibited Topics */}
                <div className="flex flex-col gap-4">
                  <label className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foregroundbase">
                    Prohibited Topics
                  </label>
                  <div className="space-y-2">
                    <div className="flex flex-wrap gap-2">
                      {values.prohibitedTopics?.map((topic, index) => (
                        <Badge
                          key={index}
                          variant="secondary"
                          className="flex items-center gap-1"
                        >
                          {topic}
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-4 w-4 p-0 hover:bg-transparent"
                            onClick={() => {
                              const newTopics =
                                values.prohibitedTopics?.filter(
                                  (_, i) => i !== index
                                ) || [];
                              setFieldValue("prohibitedTopics", newTopics);
                            }}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </Badge>
                      ))}
                    </div>
                    <ProhibitedTopicsInput
                      values={values}
                      setFieldValue={setFieldValue}
                    />
                  </div>
                </div>

                {/* Additional Options */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2">
                    <Checkbox
                      id="required-disclosures"
                      checked={values.requiredDisclosures}
                      onCheckedChange={(checked) =>
                        setFieldValue("requiredDisclosures", checked)
                      }
                    />
                    <label
                      htmlFor="required-disclosures"
                      className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foreground-60 cursor-pointer"
                    >
                      Require disclosure statements for sponsored content
                    </label>
                  </div>

                  <div className="flex items-center gap-2">
                    <Checkbox
                      id="social-media-promotion"
                      checked={values.socialMediaPromotion}
                      onCheckedChange={(checked) =>
                        setFieldValue("socialMediaPromotion", checked)
                      }
                    />
                    <label
                      htmlFor="social-media-promotion"
                      className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foreground-60 cursor-pointer"
                    >
                      Include social media promotion
                    </label>
                  </div>
                </div>

                {/* Meta Data Requirements */}
                <div className="flex flex-col gap-2">
                  <label className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foregroundbase">
                    Meta Data Requirements
                  </label>
                  <Textarea
                    className="h-[80px] bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm resize-none"
                    placeholder="SEO requirements, meta descriptions, keywords, etc..."
                    value={values.metaDataRequirements}
                    onChange={(e) =>
                      setFieldValue("metaDataRequirements", e.target.value)
                    }
                    maxLength={500}
                  />
                  <div className="flex justify-between items-center">
                    {errors.metaDataRequirements &&
                      touched.metaDataRequirements && (
                        <span className="text-sm text-errorbase">
                          {errors.metaDataRequirements}
                        </span>
                      )}
                    <span className="text-xs text-foreground-60 ml-auto">
                      {values.metaDataRequirements?.length || 0}/500
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Helper component for prohibited topics input
function ProhibitedTopicsInput({
  values,
  setFieldValue,
}: {
  values: any;
  setFieldValue: any;
}) {
  const [inputValue, setInputValue] = useState("");

  const addTopic = () => {
    const topic = inputValue.trim();
    if (topic && !values.prohibitedTopics?.includes(topic)) {
      setFieldValue("prohibitedTopics", [
        ...(values.prohibitedTopics || []),
        topic,
      ]);
      setInputValue("");
    }
  };

  return (
    <div className="flex gap-2">
      <Input
        className="flex-1 h-9 bg-uicard border border-solid border-[#eaeaea]"
        placeholder="Add prohibited topic..."
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyPress={(e) => {
          if (e.key === "Enter") {
            e.preventDefault();
            addTopic();
          }
        }}
      />
      <Button type="button" variant="outline" size="sm" onClick={addTopic}>
        <Plus className="h-4 w-4" />
      </Button>
    </div>
  );
}

import React from "react";
import { FormikErrors, FormikTouched } from "formik";
import { WebsiteFormSchema } from "@/lib/validation";
import { Card, CardContent } from "./ui/card";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";
import { Checkbox } from "./ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./ui/select";
import { categories, languageOptions, countryOptions } from "@/lib/utils";

interface WebsiteDetailsSectionProps {
  values: WebsiteFormSchema;
  setFieldValue: (field: string, value: any) => void;
  errors: FormikErrors<WebsiteFormSchema>;
  touched: FormikTouched<WebsiteFormSchema>;
}

export function WebsiteDetailsSection({
  values,
  setFieldValue,
  errors,
  touched,
}: WebsiteDetailsSectionProps) {
  const handleCategoryChange = (categoryId: string, checked: boolean) => {
    const updatedCategories = checked
      ? [...values.categories, categoryId]
      : values.categories.filter((id) => id !== categoryId);
    setFieldValue("categories", updatedCategories);
  };

  return (
    <div className="flex flex-col items-start gap-5 w-full shadow-shadow-sm">
      <h2 className="font-heading-h3 text-[length:var(--heading-h3-font-size)] tracking-[var(--heading-h3-letter-spacing)] leading-[var(--heading-h3-line-height)] text-foregroundbase">
        Website detail
      </h2>
      <Card className="w-full p-6 bg-uicard rounded-md">
        <CardContent className="p-0 flex flex-col gap-[31px]">
          <div className="flex flex-col items-start justify-center gap-8">
            {/* Website URL and Language */}
            <div className="flex flex-col lg:flex-row items-start gap-8 w-full">
              <div className="flex flex-col w-full lg:w-[264px] items-start gap-2">
                <label className="font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase">
                  Enter website *
                </label>
                <Input
                  className="bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm"
                  placeholder="https://example.com"
                  value={values.websiteUrl}
                  onChange={(e) => setFieldValue("websiteUrl", e.target.value)}
                />
                {errors.websiteUrl && touched.websiteUrl && (
                  <span className="text-sm text-errorbase">
                    {errors.websiteUrl}
                  </span>
                )}
              </div>

              <div className="flex flex-col w-full lg:w-[264px] items-start gap-2">
                <label className="font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase">
                  Website's Primary language *
                </label>
                <Select
                  value={values.primaryLanguage}
                  onValueChange={(value) =>
                    setFieldValue("primaryLanguage", value)
                  }
                >
                  <SelectTrigger className="w-full bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-[21px] h-3.5 flex items-center justify-center">
                        <span className="text-lg">
                          {languageOptions.find(
                            (opt) => opt.value === values.primaryLanguage
                          )?.flag || "🇺🇸"}
                        </span>
                      </div>
                      <span className="flex-1 text-left">
                        {languageOptions.find(
                          (opt) => opt.value === values.primaryLanguage
                        )?.label || "Select language"}
                      </span>
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    {languageOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{option.flag}</span>
                          {option.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.primaryLanguage && touched.primaryLanguage && (
                  <span className="text-sm text-errorbase">
                    {errors.primaryLanguage}
                  </span>
                )}
              </div>

              <div className="flex flex-col w-full lg:w-[264px] items-start gap-2">
                <label className="font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase">
                  Your Majority of traffic comes from *
                </label>
                <Select
                  value={values.trafficCountry}
                  onValueChange={(value) =>
                    setFieldValue("trafficCountry", value)
                  }
                >
                  <SelectTrigger className="w-full bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-[21px] h-3.5 flex items-center justify-center">
                        <span className="text-lg">
                          {countryOptions.find(
                            (opt) => opt.value === values.trafficCountry
                          )?.flag || "🇺🇸"}
                        </span>
                      </div>
                      <span className="flex-1 text-left">
                        {countryOptions.find(
                          (opt) => opt.value === values.trafficCountry
                        )?.label || "Select country"}
                      </span>
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    {countryOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{option.flag}</span>
                          {option.label}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.trafficCountry && touched.trafficCountry && (
                  <span className="text-sm text-errorbase">
                    {errors.trafficCountry}
                  </span>
                )}
              </div>
            </div>

            {/* Categories */}
            <div className="flex flex-col w-full items-start gap-2">
              <div className="flex flex-wrap items-end gap-[37px_0px] w-full">
                <div className="inline-flex items-start">
                  <label className="w-[264px] font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase">
                    Main Category *
                  </label>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-0 w-full">
                {categories.map((column, colIndex) => (
                  <div
                    key={`category-column-${colIndex}`}
                    className="flex flex-col items-start"
                  >
                    {column.map((category, index) => (
                      <div
                        key={`category-${colIndex}-${index}`}
                        className="flex w-full lg:w-[218px] items-center justify-start gap-2 p-2 bg-white hover:bg-gray-50 transition-colors"
                      >
                        <div className="flex w-6 h-6 items-center justify-center">
                          <Checkbox
                            id={`${category.id}-${colIndex}-${index}`}
                            checked={values.categories.includes(category.id)}
                            onCheckedChange={(checked) =>
                              handleCategoryChange(
                                category.id,
                                checked as boolean
                              )
                            }
                            className={
                              values.categories.includes(category.id)
                                ? "bg-accentbase border-accentbase"
                                : "bg-white border border-solid border-[#eaeaea]"
                            }
                          />
                        </div>
                        <label
                          htmlFor={`${category.id}-${colIndex}-${index}`}
                          className="flex-1 font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foreground-60 cursor-pointer"
                        >
                          {category.label}
                        </label>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
              {errors.categories && touched.categories && (
                <span className="text-sm text-errorbase">
                  {errors.categories}
                </span>
              )}
            </div>

            {/* Website Description */}
            <div className="flex flex-col w-full items-start gap-2">
              <label className="font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase">
                Description of Website *
              </label>
              <Textarea
                className="h-[98px] bg-uicard border border-solid border-[#eaeaea] shadow-shadow-sm"
                placeholder="Describe your website, its content, audience, and what makes it unique..."
                value={values.description}
                onChange={(e) => setFieldValue("description", e.target.value)}
              />
              {errors.description && touched.description && (
                <span className="text-sm text-errorbase">
                  {errors.description}
                </span>
              )}
            </div>
          </div>

          {/* Website Ownership */}
          <div className="flex items-center gap-2">
            <Checkbox
              id="website-owner"
              checked={values.isOwner}
              onCheckedChange={(checked) => setFieldValue("isOwner", checked)}
              className="w-4 h-4 bg-uicard border border-solid border-[#b3b3b399]"
            />
            <label
              htmlFor="website-owner"
              className="font-label-l1-medium text-[length:var(--label-l1-medium-font-size)] tracking-[var(--label-l1-medium-letter-spacing)] leading-[var(--label-l1-medium-line-height)] text-foregroundbase cursor-pointer"
            >
              I am the owner of the website *
            </label>
          </div>
          {errors.isOwner && touched.isOwner && (
            <span className="text-sm text-errorbase">{errors.isOwner}</span>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

import Image from "next/image";
import Link from "next/link";
import React from "react";
import {
  PiWalletLight,
  PiBellLight,
  Pi<PERSON>serCircleLight,
  PiGearLight,
  PiListLight,
} from "react-icons/pi";

const Navbar = () => {
  return (
    <nav className="sticky h-[71px] top-0 z-50 bg-background-25 backdrop-blur-sm border-b border-foreground-40/20 shadow-sm">
      <div className="mx-auto px-4 md:px-[78px]">
        <div className="flex items-center justify-between h-[71px]">
          {/* Logo Section */}
          <div className="flex items-center space-x-3 flex-shrink-0">
            <Link href="/" className="flex items-center space-x-3 group">
              <div className="relative">
                <Image
                  src="/logo.png"
                  alt="Kraken Logo"
                  height={32}
                  width={32}
                  className="transition-transform duration-200 group-hover:scale-105"
                />
              </div>
              <span className="font-[family:var(--heading-h3-font-family)] text-[length:var(--heading-h3-font-size)] font-[weight:var(--heading-h3-font-weight)] tracking-[var(--heading-h3-letter-spacing)] leading-[var(--heading-h3-line-height)] text-foregroundbase">
                Kraken
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            <NavLink href="/">Marketplace</NavLink>
            <NavLink href="/" active>
              My Websites
            </NavLink>
            <NavLink href="/">My Orders</NavLink>
            <NavLink href="/">My Projects</NavLink>
            <NavLink href="/">Received Orders</NavLink>
          </div>

          {/* Right Section - Actions */}
          <div className="flex items-center space-x-2">
            {/* Desktop Actions */}
            <div className="hidden sm:flex items-center space-x-2">
              <ActionButton icon={PiWalletLight} label="Wallet" />
              <ActionButton icon={PiBellLight} label="Notifications" />
              <ActionButton icon={PiGearLight} label="Settings" />
              <ActionButton icon={PiUserCircleLight} label="Profile" />
            </div>

            {/* Mobile Menu Button */}
            <button
              className="md:hidden p-3 rounded-lg text-foreground-60 hover:text-foregroundbase hover:bg-foreground-40/10 transition-colors duration-200"
              aria-label="Open menu"
            >
              <PiListLight className="w-7 h-7" />
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
};

// Navigation Link Component
interface NavLinkProps {
  href: string;
  children: React.ReactNode;
  active?: boolean;
}

const NavLink: React.FC<NavLinkProps> = ({
  href,
  children,
  active = false,
}) => {
  return (
    <Link
      href={href}
      className={`
        relative px-8 py-3 font-[family:var(--text-md-normal-font-family)] text-[length:var(--text-md-normal-font-size)] font-[weight:var(--text-md-normal-font-weight)] leading-[var(--text-md-normal-line-height)] rounded-lg transition-all duration-200 hover:bg-foreground-40/10
        ${
          active
            ? "text-accentbase bg-accentbase/10"
            : "text-foregroundbase hover:text-foregroundbase"
        }

      `}
    >
      {children}
      {active && (
        <span className="absolute -bottom-3 left-3 right-3 h-0.5 bg-accentbase rounded-full" />
      )}
    </Link>
  );
};

// Action Button Component
interface ActionButtonProps {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  badge?: string;
  variant?: "default" | "primary";
}

const ActionButton: React.FC<ActionButtonProps> = ({
  icon: Icon,
  label,
  badge,
  variant = "default",
}) => {
  return (
    <button
      className={`
        relative p-3 rounded-lg transition-all duration-200 focus:outline-none hover:bg-foreground-40/10
        ${
          variant === "primary"
            ? "text-accentbase hover:text-accentbase/80 "
            : "text-foreground-60 hover:text-foregroundbase "
        }
      `}
      aria-label={label}
      title={label}
    >
      <Icon className="w-6 h-6" />
      {badge && (
        <span className="absolute -top-1 -right-1 bg-errorbase text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
          {badge}
        </span>
      )}
    </button>
  );
};

export default Navbar;

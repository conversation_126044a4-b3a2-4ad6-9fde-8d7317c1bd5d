"use client";

import React, { useC<PERSON>back, memo, useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Formik, Form } from "formik";
import { websiteFormSchema, type WebsiteFormSchema } from "@/lib/validation";
import { useFormStore } from "@/store/formStore";
import { InfoCard } from "./InfoCard";
import { PreconditionsAlert } from "./PreconditionsAlert";
import { WebsiteDetailsSection } from "./WebsiteDetailsSection";
import { CreateOfferSection } from "./CreateOfferSection";
import { ArticleSpecificationSection } from "./ArticleSpecificationSection";
import { Button } from "./ui/button";
import { Alert, AlertDescription } from "./ui/alert";
import { CheckCircle, AlertCircle } from "lucide-react";
import { toast } from "sonner";

interface AddWebsiteFormProps {
  isEditMode?: boolean;
}

// Custom function to convert Zod schema to Formik validation
const createFormikValidation = (schema: typeof websiteFormSchema) => {
  return (values: WebsiteFormSchema) => {
    try {
      schema.parse(values);
      console.log("Validation passed for values:", values);
      return {};
    } catch (error: any) {
      console.log("Validation failed:", error);
      const formikErrors: Record<string, string> = {};

      if (error.errors) {
        error.errors.forEach((err: any) => {
          const path = err.path.join(".");
          formikErrors[path] = err.message;
        });
      }

      console.log("Formik errors:", formikErrors);
      return formikErrors;
    }
  };
};

function AddWebsiteFormComponent({ isEditMode = false }: AddWebsiteFormProps) {
  const router = useRouter();
  const [notification, setNotification] = useState<{
    type: "success" | "error";
    message: string;
  } | null>(null);

  const {
    formData,
    updateFormData,
    isSubmitting,
    setSubmitting,
    editingWebsiteId,
    addWebsite,
    updateWebsite,
    resetForm,
  } = useFormStore();

  const initialValues: WebsiteFormSchema = {
    websiteUrl: formData.websiteUrl || "",
    primaryLanguage: formData.primaryLanguage || "english",
    trafficCountry: formData.trafficCountry || "us",
    categories: formData.categories || [],
    description: formData.description || "",
    isOwner: formData.isOwner || false,
    guestPostingPrice: formData.guestPostingPrice || 54,
    linkInsertionPrice: formData.linkInsertionPrice || 54,
    greyNicheOffers: formData.greyNicheOffers || {
      casino: { guestPostPrice: 0, linkInsertionPrice: 0 },
      cbd: { guestPostPrice: 0, linkInsertionPrice: 0 },
      crypto: { guestPostPrice: 0, linkInsertionPrice: 0 },
      forex: { guestPostPrice: 0, linkInsertionPrice: 0 },
      adult: { guestPostPrice: 0, linkInsertionPrice: 0 },
      vaping: { guestPostPrice: 0, linkInsertionPrice: 0 },
    },
    homepageOffer: formData.homepageOffer || {
      price: 0,
      description: "",
    },
    isWritingIncluded: formData.isWritingIncluded || "yes",
    wordCountType: formData.wordCountType || "unlimited",
    minWords: formData.minWords || 0,
    maxWords: formData.maxWords || 0,
    allowDofollow: formData.allowDofollow || "yes",
    linkType: formData.linkType || "brand",
    taggingPolicy: formData.taggingPolicy || "no-tag",
    linkNumberType: formData.linkNumberType || "unlimited",
    minLinks: formData.minLinks || 0,
    maxLinks: formData.maxLinks || 0,
    otherLinksPolicy: formData.otherLinksPolicy || "no-allow",
    contentRules: formData.contentRules || "",
    acceptedContentTypes: formData.acceptedContentTypes || ["How-to guides"],
    turnaroundTime: formData.turnaroundTime || 7,
    revisionPolicy: formData.revisionPolicy || "one-revision",
    contentGuidelines: formData.contentGuidelines || "",
    prohibitedTopics: formData.prohibitedTopics || [],
    requiredDisclosures: formData.requiredDisclosures || false,
    socialMediaPromotion: formData.socialMediaPromotion || false,
    metaDataRequirements: formData.metaDataRequirements || "",
  };

  const handleSubmit = useCallback(
    async (values: WebsiteFormSchema) => {
      console.log("Form submitted with values:", values);
      setSubmitting(true);
      updateFormData(values);

      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 2000));

        if (isEditMode && editingWebsiteId) {
          // Update existing website
          updateWebsite(editingWebsiteId, values);
          const successMessage = "Website updated successfully!";
          setNotification({
            type: "success",
            message: successMessage,
          });
          toast.success(successMessage);
        } else {
          // Add new website - ensure all required fields are set
          const websiteData = {
            ...values,
            minWords: values.minWords || 0,
            maxWords: values.maxWords || 0,
            minLinks: values.minLinks || 0,
            maxLinks: values.maxLinks || 0,
            contentRules: values.contentRules || "",
            contentGuidelines: values.contentGuidelines || "",
            metaDataRequirements: values.metaDataRequirements || "",
            homepageOffer: {
              price: values.homepageOffer.price,
              description: values.homepageOffer.description || "",
            },
            status: "active" as const,
          };
          addWebsite(websiteData);
          const successMessage = "Website added successfully!";
          setNotification({
            type: "success",
            message: successMessage,
          });
          toast.success(successMessage);
          resetForm();
        }

        // Navigate back to the list after a short delay to show the success message
        setTimeout(() => {
          toast.success("Redirecting to website list...");
          router.push("/");
        }, 1500);
      } catch (error) {
        console.error("Submission error:", error);
        let errorMessage = "Error submitting form. Please try again.";

        // Handle specific error types
        if (error instanceof Error) {
          errorMessage = error.message;
        } else if (typeof error === "string") {
          errorMessage = error;
        }

        setNotification({
          type: "error",
          message: errorMessage,
        });
        toast.error(errorMessage);
      } finally {
        setSubmitting(false);
      }
    },
    [
      isEditMode,
      editingWebsiteId,
      updateFormData,
      updateWebsite,
      addWebsite,
      resetForm,
      router,
      setSubmitting,
      setNotification,
    ]
  );

  return (
    <div className="relative w-full bg-background-25 px-4 md:px-[78px] py-6">
      <h1 className="font-heading-h2 text-[length:var(--heading-h2-font-size)] tracking-[var(--heading-h2-letter-spacing)] leading-[var(--heading-h2-line-height)] text-foregroundbase mb-12">
        {isEditMode ? "Edit website" : "Add a website"}
      </h1>

      <div className="flex flex-col w-full items-center gap-16">
        {/* Notification */}
        {notification && (
          <Alert
            className={`max-w-2xl ${
              notification.type === "success"
                ? "border-green-200 bg-green-50"
                : "border-red-200 bg-red-50"
            }`}
          >
            {notification.type === "success" ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <AlertCircle className="h-4 w-4 text-red-600" />
            )}
            <AlertDescription
              className={
                notification.type === "success"
                  ? "text-green-800"
                  : "text-red-800"
              }
            >
              {notification.message}
            </AlertDescription>
          </Alert>
        )}

        <InfoCard />

        <Formik
          initialValues={initialValues}
          validate={createFormikValidation(websiteFormSchema)}
          onSubmit={handleSubmit}
          enableReinitialize
        >
          {({ values, setFieldValue, errors, touched, isValid }) => {
            console.log("Form validation state:", { isValid, errors });

            // Auto-save form data as user types (with debouncing)
            useEffect(() => {
              const timeoutId = setTimeout(() => {
                updateFormData(values);
              }, 500); // 500ms debounce

              return () => clearTimeout(timeoutId);
            }, [values]);

            // Show toast for validation errors only when form submission is attempted
            const handleFormSubmit = (e: React.FormEvent) => {
              if (Object.keys(errors).length > 0) {
                const firstError = Object.values(errors)[0];
                if (typeof firstError === "string") {
                  toast.error(`Validation Error: ${firstError}`);
                }
              }
            };

            return (
              <Form
                className="flex flex-col items-center gap-[73px] w-full"
                onSubmit={handleFormSubmit}
              >
                <PreconditionsAlert />

                <WebsiteDetailsSection
                  values={values}
                  setFieldValue={setFieldValue}
                  errors={errors}
                  touched={touched}
                />

                <CreateOfferSection
                  values={values}
                  setFieldValue={setFieldValue}
                  errors={errors}
                  touched={touched}
                />

                <ArticleSpecificationSection
                  values={values}
                  setFieldValue={setFieldValue}
                  errors={errors}
                  touched={touched}
                />

                <div className="flex justify-center gap-4 w-full">
                  <Button
                    type="button"
                    variant="outline"
                    size="lg"
                    onClick={() => window.history.back()}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    size="lg"
                    disabled={isSubmitting}
                    className="bg-accentbase hover:bg-accentbase/90 text-white min-w-[120px]"
                  >
                    {isSubmitting
                      ? isEditMode
                        ? "Updating..."
                        : "Adding..."
                      : isEditMode
                      ? "Update Website"
                      : "Add Website"}
                  </Button>
                </div>
              </Form>
            );
          }}
        </Formik>
      </div>
    </div>
  );
}

export const AddWebsiteForm = memo(AddWebsiteFormComponent);

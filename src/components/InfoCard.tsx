import React from "react";

import { Card } from "./ui/card";
import { learningPoints } from "@/lib/utils";
import Image from "next/image";

export function InfoCard() {
  return (
    <Card className="w-full p-6 flex flex-col lg:flex-row items-center gap-8 lg:gap-[193px] bg-uicard">
      <div className="flex flex-col w-full lg:w-[406px] items-start gap-[17px]">
        <h2 className="font-heading-h3 text-[length:var(--heading-h3-font-size)] tracking-[var(--heading-h3-letter-spacing)] leading-[var(--heading-h3-line-height)] text-foregroundbase">
          Learn how to get best out of linksera
        </h2>
        <div className="flex flex-col w-full lg:w-[400px] items-start gap-2">
          {learningPoints.map((point, index) => (
            <li
              key={`learning-point-${index}`}
              className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foreground-60"
            >
              {point}
            </li>
          ))}
        </div>
      </div>

      <div className="relative w-full lg:w-[628px] h-[321px] bg-foregroundbase rounded-md overflow-hidden">
        <Image
          src="/header.png"
          alt="Info Card"
          fill
          className="object-cover"
        />
      </div>
    </Card>
  );
}

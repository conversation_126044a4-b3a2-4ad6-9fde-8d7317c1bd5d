import React, { useState } from "react";
import { CheckI<PERSON>, ChevronDownIcon, ChevronUpIcon } from "lucide-react";
import { Alert, AlertTitle } from "./ui/alert";
import { Badge } from "./ui/badge";
import { Button } from "./ui/button";

export function PreconditionsAlert() {
  const [isAccepted, setIsAccepted] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  const handleAccept = () => {
    setIsAccepted(true);
    setIsExpanded(false);
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="w-full">
      <Alert
        className={`w-full bg-secondary-bg100 rounded-md border border-solid border-[#eaeaea] transition-all duration-200 ${
          isExpanded ? "rounded-b-none" : ""
        }`}
      >
        <div className="flex h-auto items-center justify-between w-full py-2">
          <AlertTitle className="font-body-b5 text-[length:var(--body-b5-font-size)] tracking-[var(--body-b5-letter-spacing)] leading-[var(--body-b5-line-height)] text-foregroundbase">
            Hey, Accept Preconditions before you start the listing!
          </AlertTitle>
          <div className="flex items-center justify-center gap-2.5">
            <Badge
              className={`flex h-[31px] items-center gap-[8px] rounded-[21px] px-2.5 py-2 transition-colors ${
                isAccepted
                  ? "bg-green-100 text-green-800 border-green-200"
                  : "bg-orange-100 text-orange-800 border-orange-200"
              }`}
            >
              {/* Colored bullet/dot */}
              <div
                className={`w-2 h-2 rounded-full ${
                  isAccepted ? "bg-green-600" : "bg-orange-600"
                }`}
              />
              <span className="font-dm-sans font-medium text-[13px] leading-[18.3px]">
                {isAccepted ? "Accepted" : "Pending"}
              </span>
            </Badge>
            <button
              onClick={toggleExpanded}
              className="w-5 h-5 cursor-pointer hover:text-foreground-60 transition-colors"
            >
              {isExpanded ? (
                <ChevronUpIcon className="w-5 h-5" />
              ) : (
                <ChevronDownIcon className="w-5 h-5" />
              )}
            </button>
          </div>
        </div>
      </Alert>

      {/* Accordion Content */}
      {isExpanded && (
        <div className="w-full bg-secondary-bg100 border border-solid border-[#eaeaea] border-t-0 rounded-b-md p-6 animate-in slide-in-from-top-2 duration-200">
          <div className="space-y-4">
            <p className="text-sm text-foreground-60 leading-relaxed">
              Before you can proceed with your listing, please make sure to
              review all required preconditions. Accepting these is mandatory to
              continue. It ensures your submission meets our platform standards
              and avoids delays. Listings that don't meet these terms may be
              rejected. Take a moment to go through them carefully before moving
              ahead. Once accepted, you'll be able to start listing right away.
            </p>

            {!isAccepted && (
              <div className="flex justify-start">
                <Button
                  onClick={handleAccept}
                  className="bg-accentbase hover:bg-accentbase/90 text-white px-6 py-2 rounded-md transition-colors"
                >
                  Accept
                </Button>
              </div>
            )}

            {isAccepted && (
              <div className="flex items-center gap-2 text-green-700">
                <CheckIcon className="w-4 h-4" />
                <span className="text-sm font-medium">
                  Preconditions accepted successfully!
                </span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

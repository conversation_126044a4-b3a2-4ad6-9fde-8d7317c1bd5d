"use client";

import React, { useMemo, useCallback, memo } from "react";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";

import { useFormStore } from "@/store/formStore";
import { ChevronLeft, ChevronRight } from "lucide-react";
import {
  FaDice,
  FaLeaf,
  FaBitcoin,
  FaDollarSign,
  FaHeart,
  FaSmoking,
} from "react-icons/fa";
import { countryOptions, languageOptions } from "@/lib/utils";

// Grey niche icons mapping
const greyNicheIcons = {
  casino: FaDice,
  cbd: FaLeaf,
  crypto: FaBitcoin,
  forex: FaDollarSign,
  adult: FaHeart,
  vaping: FaSmoking,
};

// Helper functions to get country and language display info
const getCountryDisplay = (countryCode: string) => {
  const country = countryOptions.find((opt) => opt.value === countryCode);
  return country
    ? { name: country.label, flag: country.flag }
    : { name: countryCode, flag: "🏳️" };
};

const getLanguageDisplay = (languageCode: string) => {
  const language = languageOptions.find((opt) => opt.value === languageCode);
  return language
    ? { name: language.label, flag: language.flag }
    : { name: languageCode, flag: "🏳️" };
};

function DataTableComponent() {
  const router = useRouter();
  const {
    websites,
    pagination,
    isLoading,
    setCurrentPage,
    loadWebsiteForEdit,
  } = useFormStore();

  // Show all websites without filtering
  const filteredWebsites = websites;

  const paginatedWebsites = useMemo(() => {
    const startIndex = (pagination.currentPage - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return filteredWebsites.slice(startIndex, endIndex);
  }, [filteredWebsites, pagination.currentPage, pagination.pageSize]);

  const totalPages = Math.ceil(filteredWebsites.length / pagination.pageSize);

  // Handle row click to edit website
  const handleRowClick = useCallback(
    (websiteId: string) => {
      loadWebsiteForEdit(websiteId);
      router.push(`/add-website?edit=${websiteId}`);
    },
    [loadWebsiteForEdit, router]
  );

  // Get all grey niche icons (all 6 compulsory)
  const getAllGreyNicheIcons = useCallback(() => {
    return Object.keys(greyNicheIcons) as (keyof typeof greyNicheIcons)[];
  }, []);

  return (
    <div className="space-y-4">
      {/* Table */}
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader className="[&_tr]:border-0">
            <TableRow className="bg-[#faf8ff] border-0">
              <TableHead className="py-4 text-gray-600 text-base">
                Website
              </TableHead>
              <TableHead className="py-4 text-gray-600 text-base">
                Country
              </TableHead>
              <TableHead className="py-4 text-gray-600 text-base">
                Language
              </TableHead>
              <TableHead className="py-4 text-gray-600 text-base">
                Category
              </TableHead>
              <TableHead className="py-4 text-gray-600 text-base">
                Grey Niche
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="[&_tr:last-child]:border-0 [&_tr]:border-0">
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  Loading...
                </TableCell>
              </TableRow>
            ) : paginatedWebsites.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8">
                  No websites found
                </TableCell>
              </TableRow>
            ) : (
              paginatedWebsites.map((website, index) => {
                const countryDisplay = getCountryDisplay(
                  website.trafficCountry
                );
                const languageDisplay = getLanguageDisplay(
                  website.primaryLanguage
                );

                return (
                  <TableRow
                    key={website.id}
                    className={`border-0 hover:bg-gray-50 ${
                      index % 2 === 0 ? "bg-white" : "bg-[#faf8ff]"
                    }`}
                  >
                    <TableCell className="py-4 text-black text-base font-medium">
                      {website.websiteUrl}
                    </TableCell>
                    <TableCell className="py-4 text-black text-base">
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{countryDisplay.flag}</span>
                        {countryDisplay.name}
                      </div>
                    </TableCell>
                    <TableCell className="py-4 text-black text-base">
                      {languageDisplay.name}
                    </TableCell>
                    <TableCell className="py-4 text-black text-base">
                      {website.categories.join(", ")}
                    </TableCell>
                    <TableCell className="py-4">
                      <div className="flex gap-2">
                        {getAllGreyNicheIcons().map((niche) => {
                          const IconComponent = greyNicheIcons[niche];
                          return (
                            <span
                              key={niche}
                              className="text-base text-gray-600"
                              title={niche}
                            >
                              <IconComponent />
                            </span>
                          );
                        })}
                      </div>
                    </TableCell>
                  </TableRow>
                );
              })
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {(pagination.currentPage - 1) * pagination.pageSize + 1} to{" "}
            {Math.min(
              pagination.currentPage * pagination.pageSize,
              filteredWebsites.length
            )}{" "}
            of {filteredWebsites.length} results
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(pagination.currentPage - 1)}
              disabled={pagination.currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <div className="flex gap-1">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                (page) => (
                  <Button
                    key={page}
                    variant={
                      page === pagination.currentPage ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() => setCurrentPage(page)}
                    className="w-8 h-8"
                  >
                    {page}
                  </Button>
                )
              )}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(pagination.currentPage + 1)}
              disabled={pagination.currentPage === totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

export const DataTable = memo(DataTableComponent);

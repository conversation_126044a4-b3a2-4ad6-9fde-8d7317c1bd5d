"use client";

import React, { useMemo, use<PERSON><PERSON>back, memo } from "react";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import { useFormStore } from "@/store/formStore";
import { Website } from "@/types/form";
import { ChevronLeft, ChevronRight, Search, Edit, Trash2 } from "lucide-react";
import {
  FaDice,
  FaLeaf,
  FaBitcoin,
  FaDollarSign,
  FaHeart,
  FaSmoking,
} from "react-icons/fa";

// Grey niche icons mapping
const greyNicheIcons = {
  casino: FaDice,
  cbd: FaLeaf,
  crypto: FaBitcoin,
  forex: FaDollarSign,
  adult: FaHeart,
  vaping: FaSmoking,
};

// Country code to name mapping
const countryNames: Record<string, string> = {
  us: "United States",
  ca: "Canada",
  uk: "United Kingdom",
  au: "Australia",
  de: "Germany",
  fr: "France",
  es: "Spain",
  it: "Italy",
  jp: "Japan",
  kr: "South Korea",
  in: "India",
  br: "Brazil",
  mx: "Mexico",
  nl: "Netherlands",
  se: "Sweden",
  no: "Norway",
  dk: "Denmark",
  fi: "Finland",
};

// Language code to name mapping
const languageNames: Record<string, string> = {
  english: "English",
  spanish: "Spanish",
  french: "French",
  german: "German",
  italian: "Italian",
  portuguese: "Portuguese",
  dutch: "Dutch",
  swedish: "Swedish",
  norwegian: "Norwegian",
  danish: "Danish",
  finnish: "Finnish",
  japanese: "Japanese",
  korean: "Korean",
  hindi: "Hindi",
};

function DataTableComponent() {
  const router = useRouter();
  const {
    websites,
    pagination,
    filters,
    isLoading,
    setCurrentPage,
    setFilters,
    deleteWebsite,
    loadWebsiteForEdit,
  } = useFormStore();

  // Filter and paginate websites
  const filteredWebsites = useMemo(() => {
    return websites.filter((website) => {
      const matchesSearch =
        !filters.search ||
        website.websiteUrl
          .toLowerCase()
          .includes(filters.search.toLowerCase()) ||
        website.description
          .toLowerCase()
          .includes(filters.search.toLowerCase());

      const matchesLanguage =
        !filters.language || website.primaryLanguage === filters.language;

      const matchesCountry =
        !filters.country || website.trafficCountry === filters.country;

      const matchesCategory =
        !filters.category || website.categories.includes(filters.category);

      return (
        matchesSearch && matchesLanguage && matchesCountry && matchesCategory
      );
    });
  }, [websites, filters]);

  const paginatedWebsites = useMemo(() => {
    const startIndex = (pagination.currentPage - 1) * pagination.pageSize;
    const endIndex = startIndex + pagination.pageSize;
    return filteredWebsites.slice(startIndex, endIndex);
  }, [filteredWebsites, pagination.currentPage, pagination.pageSize]);

  const totalPages = Math.ceil(filteredWebsites.length / pagination.pageSize);

  // Memoized filter handlers
  const handleSearchChange = useCallback(
    (value: string) => {
      setFilters({ search: value });
    },
    [setFilters]
  );

  const handleLanguageChange = useCallback(
    (value: string) => {
      setFilters({ language: value });
    },
    [setFilters]
  );

  const handleCountryChange = useCallback(
    (value: string) => {
      setFilters({ country: value });
    },
    [setFilters]
  );

  const handleRowClick = useCallback(
    (websiteId: string) => {
      loadWebsiteForEdit(websiteId);
      router.push(`/add-website?edit=${websiteId}`);
    },
    [loadWebsiteForEdit, router]
  );

  const handleDelete = useCallback(
    (e: React.MouseEvent, websiteId: string) => {
      e.stopPropagation();
      if (confirm("Are you sure you want to delete this website?")) {
        deleteWebsite(websiteId);
      }
    },
    [deleteWebsite]
  );

  const getActiveGreyNiches = useCallback((website: Website): string[] => {
    const activeNiches: string[] = [];
    Object.entries(website.greyNicheOffers).forEach(([niche, offer]) => {
      if (offer.guestPostPrice > 0 || offer.linkInsertionPrice > 0) {
        activeNiches.push(niche);
      }
    });
    return activeNiches;
  }, []);

  const generateOffersSummary = useCallback((website: Website): string => {
    const offers = [];
    if (website.guestPostingPrice > 0)
      offers.push(`GP: $${website.guestPostingPrice}`);
    if (website.linkInsertionPrice > 0)
      offers.push(`LI: $${website.linkInsertionPrice}`);
    if (website.homepageOffer.price > 0)
      offers.push(`HP: $${website.homepageOffer.price}`);
    return offers.join(", ") || "No offers";
  }, []);

  return (
    <div className="space-y-4">
      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4 items-center">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search websites..."
            value={filters.search}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <select
            value={filters.language}
            onChange={(e) => handleLanguageChange(e.target.value)}
            className="px-3 py-2 border rounded-md text-sm"
          >
            <option value="">All Languages</option>
            {Object.entries(languageNames).map(([code, name]) => (
              <option key={code} value={code}>
                {name}
              </option>
            ))}
          </select>
          <select
            value={filters.country}
            onChange={(e) => handleCountryChange(e.target.value)}
            className="px-3 py-2 border rounded-md text-sm"
          >
            <option value="">All Countries</option>
            {Object.entries(countryNames).map(([code, name]) => (
              <option key={code} value={code}>
                {name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader className="[&_tr]:border-0">
            <TableRow className="bg-[#faf8ff] border-0">
              <TableHead className="py-4 text-gray-600 text-base">
                Website URL
              </TableHead>
              <TableHead className="py-4 text-gray-600 text-base">
                Primary Language
              </TableHead>
              <TableHead className="py-4 text-gray-600 text-base">
                Country
              </TableHead>
              <TableHead className="py-4 text-gray-600 text-base">
                Offers Summary
              </TableHead>
              <TableHead className="py-4 text-gray-600 text-base">
                Grey Niches
              </TableHead>
              <TableHead className="py-4 text-gray-600 text-base">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody className="[&_tr:last-child]:border-0 [&_tr]:border-0">
            {isLoading ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  Loading...
                </TableCell>
              </TableRow>
            ) : paginatedWebsites.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  No websites found
                </TableCell>
              </TableRow>
            ) : (
              paginatedWebsites.map((website, index) => (
                <TableRow
                  key={website.id}
                  onClick={() => handleRowClick(website.id)}
                  className={`border-0 cursor-pointer hover:bg-gray-50 ${
                    index % 2 === 0 ? "bg-white" : "bg-[#faf8ff]"
                  }`}
                >
                  <TableCell className="py-4 text-black text-base font-medium">
                    {website.websiteUrl}
                  </TableCell>
                  <TableCell className="py-4 text-black text-base">
                    {languageNames[website.primaryLanguage] ||
                      website.primaryLanguage}
                  </TableCell>
                  <TableCell className="py-4 text-black text-base">
                    {countryNames[website.trafficCountry] ||
                      website.trafficCountry}
                  </TableCell>
                  <TableCell className="py-4 text-black text-base">
                    {generateOffersSummary(website)}
                  </TableCell>
                  <TableCell className="py-4">
                    <div className="flex gap-2">
                      {getActiveGreyNiches(website).map((niche) => {
                        const IconComponent =
                          greyNicheIcons[niche as keyof typeof greyNicheIcons];
                        return IconComponent ? (
                          <span
                            key={niche}
                            className="text-base text-gray-600"
                            title={niche}
                          >
                            <IconComponent />
                          </span>
                        ) : null;
                      })}
                    </div>
                  </TableCell>
                  <TableCell className="py-4">
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleRowClick(website.id);
                        }}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={(e) => handleDelete(e, website.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {(pagination.currentPage - 1) * pagination.pageSize + 1} to{" "}
            {Math.min(
              pagination.currentPage * pagination.pageSize,
              filteredWebsites.length
            )}{" "}
            of {filteredWebsites.length} results
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(pagination.currentPage - 1)}
              disabled={pagination.currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <div className="flex gap-1">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map(
                (page) => (
                  <Button
                    key={page}
                    variant={
                      page === pagination.currentPage ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() => setCurrentPage(page)}
                    className="w-8 h-8"
                  >
                    {page}
                  </Button>
                )
              )}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(pagination.currentPage + 1)}
              disabled={pagination.currentPage === totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}

export const DataTable = memo(DataTableComponent);

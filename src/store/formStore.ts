import { create } from "zustand";
import { persist } from "zustand/middleware";
import {
  WebsiteFormData,
  Website,
  PaginationState,
  TableFilters,
} from "@/types/form";

interface FormStore {
  // Form State
  formData: Partial<WebsiteFormData>;
  currentStep: number;
  isSubmitting: boolean;
  editingWebsiteId: string | null;

  // Website List State
  websites: Website[];
  pagination: PaginationState;
  filters: TableFilters;
  isLoading: boolean;

  // Form Actions
  updateFormData: (data: Partial<WebsiteFormData>) => void;
  setCurrentStep: (step: number) => void;
  setSubmitting: (submitting: boolean) => void;
  resetForm: () => void;
  setEditingWebsite: (websiteId: string | null) => void;
  loadWebsiteForEdit: (websiteId: string) => void;

  // Website List Actions
  addWebsite: (
    website: Omit<Website, "id" | "createdAt" | "updatedAt">
  ) => void;
  updateWebsite: (websiteId: string, updates: Partial<WebsiteFormData>) => void;
  deleteWebsite: (websiteId: string) => void;
  setWebsites: (websites: Website[]) => void;

  // Pagination Actions
  setPagination: (pagination: Partial<PaginationState>) => void;
  setCurrentPage: (page: number) => void;

  // Filter Actions
  setFilters: (filters: Partial<TableFilters>) => void;
  setLoading: (loading: boolean) => void;
}

const initialFormData: Partial<WebsiteFormData> = {
  websiteUrl: "",
  primaryLanguage: "english",
  trafficCountry: "us",
  categories: [],
  description: "",
  isOwner: false,
  guestPostingPrice: 54,
  linkInsertionPrice: 54,
  greyNicheOffers: {
    casino: { guestPostPrice: 0, linkInsertionPrice: 0 },
    cbd: { guestPostPrice: 0, linkInsertionPrice: 0 },
    crypto: { guestPostPrice: 0, linkInsertionPrice: 0 },
    forex: { guestPostPrice: 0, linkInsertionPrice: 0 },
    adult: { guestPostPrice: 0, linkInsertionPrice: 0 },
    vaping: { guestPostPrice: 0, linkInsertionPrice: 0 },
  },
  homepageOffer: {
    price: 0,
    description: "",
  },
  isWritingIncluded: "yes",
  wordCountType: "unlimited",
  minWords: 0,
  maxWords: 0,
  allowDofollow: "yes",
  linkType: "brand",
  taggingPolicy: "no-tag",
  linkNumberType: "unlimited",
  minLinks: 0,
  maxLinks: 0,
  otherLinksPolicy: "no-allow",
  contentRules: "",
  acceptedContentTypes: [],
  turnaroundTime: 7,
  revisionPolicy: "one-revision",
  contentGuidelines: "",
  prohibitedTopics: [],
  requiredDisclosures: false,
  socialMediaPromotion: false,
  metaDataRequirements: "",
};

const initialPagination: PaginationState = {
  currentPage: 1,
  pageSize: 10,
  totalItems: 0,
  totalPages: 0,
};

const initialFilters: TableFilters = {
  search: "",
  language: "",
  country: "",
  category: "",
};

// Generate some sample data for demonstration
const generateSampleWebsites = (): Website[] => [
  {
    id: "1",
    websiteUrl: "https://www.example.com",
    primaryLanguage: "english",
    trafficCountry: "us",
    categories: ["Technology", "Business"],
    description:
      "A leading technology blog covering the latest trends in software development and business innovation.",
    isOwner: true,
    guestPostingPrice: 150,
    linkInsertionPrice: 75,
    greyNicheOffers: {
      casino: { guestPostPrice: 300, linkInsertionPrice: 150 },
      cbd: { guestPostPrice: 200, linkInsertionPrice: 100 },
      crypto: { guestPostPrice: 250, linkInsertionPrice: 125 },
      forex: { guestPostPrice: 280, linkInsertionPrice: 140 },
      adult: { guestPostPrice: 0, linkInsertionPrice: 0 },
      vaping: { guestPostPrice: 180, linkInsertionPrice: 90 },
    },
    homepageOffer: {
      price: 500,
      description: "Premium homepage placement with guaranteed visibility",
    },
    isWritingIncluded: "yes",
    wordCountType: "limited",
    minWords: 800,
    maxWords: 1500,
    allowDofollow: "yes",
    linkType: "brand",
    taggingPolicy: "tag-request",
    linkNumberType: "limited",
    minLinks: 1,
    maxLinks: 3,
    otherLinksPolicy: "no-allow",
    contentRules:
      "High-quality, original content only. No promotional content.",
    acceptedContentTypes: [
      "How-to guides",
      "Industry insights",
      "Case studies",
    ],
    turnaroundTime: 5,
    revisionPolicy: "one-revision",
    contentGuidelines:
      "Professional tone, well-researched content with credible sources.",
    prohibitedTopics: ["Politics", "Religion", "Controversial topics"],
    requiredDisclosures: true,
    socialMediaPromotion: true,
    metaDataRequirements: "SEO-optimized title and meta description required",
    createdAt: new Date("2024-01-15"),
    updatedAt: new Date("2024-01-15"),
    status: "active",
  },
  {
    id: "2",
    websiteUrl: "https://www.techstore.com",
    primaryLanguage: "english",
    trafficCountry: "ca",
    categories: ["E-commerce", "Technology"],
    description:
      "Online electronics store with comprehensive product reviews and buying guides.",
    isOwner: true,
    guestPostingPrice: 120,
    linkInsertionPrice: 60,
    greyNicheOffers: {
      casino: { guestPostPrice: 0, linkInsertionPrice: 0 },
      cbd: { guestPostPrice: 150, linkInsertionPrice: 75 },
      crypto: { guestPostPrice: 200, linkInsertionPrice: 100 },
      forex: { guestPostPrice: 180, linkInsertionPrice: 90 },
      adult: { guestPostPrice: 0, linkInsertionPrice: 0 },
      vaping: { guestPostPrice: 0, linkInsertionPrice: 0 },
    },
    homepageOffer: {
      price: 350,
      description: "Featured product placement on homepage",
    },
    isWritingIncluded: "yes",
    wordCountType: "unlimited",
    minWords: 0,
    maxWords: 0,
    allowDofollow: "yes",
    linkType: "mixed",
    taggingPolicy: "no-tag",
    linkNumberType: "unlimited",
    minLinks: 0,
    maxLinks: 0,
    otherLinksPolicy: "allow",
    contentRules:
      "Product-focused content preferred. Technical accuracy required.",
    acceptedContentTypes: ["Product reviews", "Buying guides", "Tech news"],
    turnaroundTime: 7,
    revisionPolicy: "unlimited-revisions",
    contentGuidelines: "Detailed, informative content with practical value.",
    prohibitedTopics: ["Competitor products", "Negative reviews"],
    requiredDisclosures: true,
    socialMediaPromotion: false,
    metaDataRequirements: "Product-focused keywords required",
    createdAt: new Date("2024-01-10"),
    updatedAt: new Date("2024-01-12"),
    status: "active",
  },
];

export const useFormStore = create<FormStore>()(
  persist(
    (set, get) => ({
      // Initial State
      formData: initialFormData,
      currentStep: 1,
      isSubmitting: false,
      editingWebsiteId: null,
      websites: generateSampleWebsites(),
      pagination: {
        ...initialPagination,
        totalItems: generateSampleWebsites().length,
        totalPages: Math.ceil(
          generateSampleWebsites().length / initialPagination.pageSize
        ),
      },
      filters: initialFilters,
      isLoading: false,

      // Form Actions
      updateFormData: (data) =>
        set((state) => ({
          formData: { ...state.formData, ...data },
        })),

      setCurrentStep: (step) => set({ currentStep: step }),

      setSubmitting: (submitting) => set({ isSubmitting: submitting }),

      resetForm: () =>
        set({
          formData: initialFormData,
          currentStep: 1,
          isSubmitting: false,
          editingWebsiteId: null,
        }),

      setEditingWebsite: (websiteId) => set({ editingWebsiteId: websiteId }),

      loadWebsiteForEdit: (websiteId) => {
        const { websites } = get();
        const website = websites.find((w) => w.id === websiteId);
        if (website) {
          const { id, createdAt, updatedAt, status, ...formData } = website;
          set({
            formData,
            editingWebsiteId: websiteId,
            currentStep: 1,
          });
        }
      },

      // Website List Actions
      addWebsite: (websiteData) =>
        set((state) => {
          const newWebsite: Website = {
            ...websiteData,
            id: Date.now().toString(),
            createdAt: new Date(),
            updatedAt: new Date(),
            status: "active",
          };
          const updatedWebsites = [...state.websites, newWebsite];
          return {
            websites: updatedWebsites,
            pagination: {
              ...state.pagination,
              totalItems: updatedWebsites.length,
              totalPages: Math.ceil(
                updatedWebsites.length / state.pagination.pageSize
              ),
            },
          };
        }),

      updateWebsite: (websiteId, updates) =>
        set((state) => {
          const updatedWebsites = state.websites.map((website) =>
            website.id === websiteId
              ? { ...website, ...updates, updatedAt: new Date() }
              : website
          );
          return { websites: updatedWebsites };
        }),

      deleteWebsite: (websiteId) =>
        set((state) => {
          const updatedWebsites = state.websites.filter(
            (w) => w.id !== websiteId
          );
          return {
            websites: updatedWebsites,
            pagination: {
              ...state.pagination,
              totalItems: updatedWebsites.length,
              totalPages: Math.ceil(
                updatedWebsites.length / state.pagination.pageSize
              ),
            },
          };
        }),

      setWebsites: (websites) =>
        set((state) => ({
          websites,
          pagination: {
            ...state.pagination,
            totalItems: websites.length,
            totalPages: Math.ceil(websites.length / state.pagination.pageSize),
          },
        })),

      // Pagination Actions
      setPagination: (pagination) =>
        set((state) => ({
          pagination: { ...state.pagination, ...pagination },
        })),

      setCurrentPage: (page) =>
        set((state) => ({
          pagination: { ...state.pagination, currentPage: page },
        })),

      // Filter Actions
      setFilters: (filters) =>
        set((state) => ({
          filters: { ...state.filters, ...filters },
        })),

      setLoading: (loading) => set({ isLoading: loading }),
    }),
    {
      name: "backlink-marketplace-store",
      partialize: (state) => ({
        formData: state.formData,
        currentStep: state.currentStep,
        editingWebsiteId: state.editingWebsiteId,
        websites: state.websites,
        pagination: state.pagination,
        filters: state.filters,
      }),
    }
  )
);

"use client";

import { AddWebsiteForm } from "@/components/AddWebsiteForm";
import { useFormStore } from "@/store/formStore";
import { useSearchParams } from "next/navigation";
import React, { useEffect } from "react";

const AddWebsitePage = () => {
  const searchParams = useSearchParams();
  const { loadWebsiteForEdit, resetForm, editingWebsiteId } = useFormStore();

  const editId = searchParams.get("edit");
  const isEditMode = Boolean(editId);

  useEffect(() => {
    if (editId) {
      // Always ensure we have the correct data loaded for the edit ID
      // This handles both initial navigation and page refresh scenarios
      if (editId !== editingWebsiteId) {
        loadWebsiteForEdit(editId);
      }
    } else if (!editId && editingWebsiteId) {
      // Reset form when switching from edit to create mode
      resetForm();
    }
  }, [editId, editingWebsiteId, loadWebsiteForEdit, resetForm]);

  return (
    <main className="min-h-screen bg-background-25">
      <AddWebsiteForm isEditMode={isEditMode} />
    </main>
  );
};

export default AddWebsitePage;
